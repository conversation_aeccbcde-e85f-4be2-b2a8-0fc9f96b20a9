{"stm32-for-vscode.openOCDPath": false, "stm32-for-vscode.makePath": false, "stm32-for-vscode.armToolchainPath": false, "C_Cpp_Runner.cCompilerPath": "gcc", "C_Cpp_Runner.cppCompilerPath": "g++", "C_Cpp_Runner.debuggerPath": "gdb", "C_Cpp_Runner.cStandard": "", "C_Cpp_Runner.cppStandard": "", "C_Cpp_Runner.msvcBatchPath": "C:/Program Files/Microsoft Visual Studio/VR_NR/Community/VC/Auxiliary/Build/vcvarsall.bat", "C_Cpp_Runner.useMsvc": false, "C_Cpp_Runner.warnings": ["-Wall", "-Wextra", "-Wpedantic", "-W<PERSON>dow", "-Wformat=2", "-Wcast-align", "-Wconversion", "-Wsign-conversion", "-W<PERSON><PERSON>-dereference"], "C_Cpp_Runner.msvcWarnings": ["/W4", "/permissive-", "/w14242", "/w14287", "/w14296", "/w14311", "/w14826", "/w44062", "/w44242", "/w14905", "/w14906", "/w14263", "/w44265", "/w14928"], "C_Cpp_Runner.enableWarnings": true, "C_Cpp_Runner.warningsAsError": false, "C_Cpp_Runner.compilerArgs": [], "C_Cpp_Runner.linkerArgs": [], "C_Cpp_Runner.includePaths": [], "C_Cpp_Runner.includeSearch": ["*", "**/*"], "C_Cpp_Runner.excludeSearch": ["**/build", "**/build/**", "**/.*", "**/.*/**", "**/.vscode", "**/.vscode/**"], "C_Cpp_Runner.useAddressSanitizer": false, "C_Cpp_Runner.useUndefinedSanitizer": false, "C_Cpp_Runner.useLeakSanitizer": false, "C_Cpp_Runner.showCompilationTime": false, "C_Cpp_Runner.useLinkTimeOptimization": false, "C_Cpp_Runner.msvcSecureNoWarnings": false}