#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M7.IPParameters=default_mode_Activation
CORTEX_M7.default_mode_Activation=1
Dma.I2C3_TX.0.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C3_TX.0.EventEnable=DISABLE
Dma.I2C3_TX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.I2C3_TX.0.Instance=DMA1_Stream0
Dma.I2C3_TX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C3_TX.0.MemInc=DMA_MINC_ENABLE
Dma.I2C3_TX.0.Mode=DMA_NORMAL
Dma.I2C3_TX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C3_TX.0.PeriphInc=DMA_PINC_DISABLE
Dma.I2C3_TX.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.I2C3_TX.0.Priority=DMA_PRIORITY_LOW
Dma.I2C3_TX.0.RequestNumber=1
Dma.I2C3_TX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.I2C3_TX.0.SignalID=NONE
Dma.I2C3_TX.0.SyncEnable=DISABLE
Dma.I2C3_TX.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.I2C3_TX.0.SyncRequestNumber=1
Dma.I2C3_TX.0.SyncSignalID=NONE
Dma.Request0=I2C3_TX
Dma.RequestsNb=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Speed_Mode=I2C_Standard
I2C1.IPParameters=Timing,I2C_Speed_Mode
I2C1.Timing=0x307075B1
I2C3.I2C_Speed_Mode=I2C_Fast_Plus
I2C3.IPParameters=Timing,I2C_Speed_Mode
I2C3.Timing=0x0050174F
KeepUserPlacement=false
MMTAppRegionsCount=0
MMTConfigApplied=false
Mcu.CPN=STM32H723ZGT6
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M7
Mcu.IP1=DEBUG
Mcu.IP10=TIM4
Mcu.IP11=USART1
Mcu.IP12=USART2
Mcu.IP13=USART6
Mcu.IP2=DMA
Mcu.IP3=I2C1
Mcu.IP4=I2C3
Mcu.IP5=MEMORYMAP
Mcu.IP6=NVIC
Mcu.IP7=RCC
Mcu.IP8=SYS
Mcu.IP9=TIM1
Mcu.IPNb=14
Mcu.Name=STM32H723ZGTx
Mcu.Package=LQFP144
Mcu.Pin0=PC14-OSC32_IN
Mcu.Pin1=PC15-OSC32_OUT
Mcu.Pin10=PB14
Mcu.Pin11=PB15
Mcu.Pin12=PD12
Mcu.Pin13=PC6
Mcu.Pin14=PC7
Mcu.Pin15=PC9
Mcu.Pin16=PA8
Mcu.Pin17=PA13(JTMS/SWDIO)
Mcu.Pin18=PA14(JTCK/SWCLK)
Mcu.Pin19=PB6
Mcu.Pin2=PH0-OSC_IN
Mcu.Pin20=PB7
Mcu.Pin21=VP_SYS_VS_Systick
Mcu.Pin22=VP_TIM4_VS_ClockSourceINT
Mcu.Pin23=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin3=PH1-OSC_OUT
Mcu.Pin4=PA0
Mcu.Pin5=PA2
Mcu.Pin6=PA3
Mcu.Pin7=PA6
Mcu.Pin8=PE9
Mcu.Pin9=PE11
Mcu.PinsNb=24
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H723ZGTx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C3_ER_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.I2C3_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:3\:0\:true\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:2\:0\:true\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0.Locked=true
PA0.Signal=GPIO_Output
PA13(JTMS/SWDIO).Locked=true
PA13(JTMS/SWDIO).Mode=Serial_Wire
PA13(JTMS/SWDIO).Signal=DEBUG_JTMS-SWDIO
PA14(JTCK/SWCLK).Locked=true
PA14(JTCK/SWCLK).Mode=Serial_Wire
PA14(JTCK/SWCLK).Signal=DEBUG_JTCK-SWCLK
PA2.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA2.GPIO_Label=USART2_TX
PA2.GPIO_PuPd=GPIO_PULLUP
PA2.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA3.GPIO_Label=USART2_RX
PA3.GPIO_PuPd=GPIO_PULLUP
PA3.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA6.Locked=true
PA6.Signal=GPIO_Output
PA8.GPIOParameters=GPIO_Speed,GPIO_Pu
PA8.GPIO_Pu=GPIO_NOPULL
PA8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA8.Mode=I2C
PA8.Signal=I2C3_SCL
PB14.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB14.GPIO_Label=USART1_TX
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB14.Locked=true
PB14.Mode=Asynchronous
PB14.Signal=USART1_TX
PB15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB15.GPIO_Label=USART1_RX
PB15.GPIO_PuPd=GPIO_PULLUP
PB15.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB15.Locked=true
PB15.Mode=Asynchronous
PB15.Signal=USART1_RX
PB6.GPIOParameters=GPIO_Speed,GPIO_Pu
PB6.GPIO_Pu=GPIO_PULLUP
PB6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.GPIOParameters=GPIO_Speed,GPIO_Pu
PB7.GPIO_Pu=GPIO_PULLUP
PB7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB7.Locked=true
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.GPIOParameters=GPIO_Speed,GPIO_PuPd
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PC9.GPIOParameters=GPIO_Speed,GPIO_Pu
PC9.GPIO_Pu=GPIO_NOPULL
PC9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC9.Mode=I2C
PC9.Signal=I2C3_SDA
PD12.GPIOParameters=GPIO_Speed,GPIO_Label
PD12.GPIO_Label=TIM4_CH1 (AF2)
PD12.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PE11.GPIOParameters=GPIO_PuPd
PE11.GPIO_PuPd=GPIO_PULLUP
PE11.Locked=true
PE11.Signal=S_TIM1_CH2
PE9.GPIOParameters=GPIO_PuPd
PE9.GPIO_PuPd=GPIO_PULLUP
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H723ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.12.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Defect detection cart.ioc
ProjectManager.ProjectName=Defect detection cart
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,4-MX_TIM4_Init-TIM4-false-HAL-true,5-MX_USART2_UART_Init-USART2-false-HAL-true,6-MX_USART1_UART_Init-USART1-false-HAL-true,7-MX_I2C1_Init-I2C1-false-HAL-true,8-MX_I2C3_Init-I2C3-false-HAL-true,9-MX_USART6_UART_Init-USART6-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true,0-MX_DMA_Init-DMA-false-HAL-true
RCC.ADCFreq_Value=50390625
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=5
RCC.DIVN1=96
RCC.DIVP1=1
RCC.DIVP1Freq_Value=*********
RCC.DIVP2Freq_Value=50390625
RCC.DIVP3Freq_Value=50390625
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=50390625
RCC.DIVQ3Freq_Value=50390625
RCC.DIVR1Freq_Value=*********
RCC.DIVR2Freq_Value=50390625
RCC.DIVR3Freq_Value=50390625
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVN1,DIVP1,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL2FRACN,PLL3FRACN,PLLFRACN,PLLSourceVirtual,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=50390625
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL3FRACN=0
RCC.PLLFRACN=0
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=*********
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBFreq_Value=*********
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=5000000
RCC.VCOInput2Freq_Value=781250
RCC.VCOInput3Freq_Value=781250
SH.S_TIM1_CH1.0=TIM1_CH1,Encoder_Interface
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,Encoder_Interface
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,PWM Generation1 CH1
SH.S_TIM4_CH1.ConfNb=1
TIM1.EncoderMode=TIM_ENCODERMODE_TI12
TIM1.IPParameters=EncoderMode
TIM4.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM4.IPParameters=Prescaler,Channel-PWM Generation1 CH1,Period,Pulse-PWM Generation1 CH1
TIM4.Period=19999
TIM4.Prescaler=119
TIM4.Pulse-PWM\ Generation1\ CH1=1500
USART1.IPParameters=VirtualMode-Asynchronous,OverSampling
USART1.OverSampling=UART_OVERSAMPLING_8
USART1.VirtualMode-Asynchronous=VM_ASYNC
USART2.BaudRate=9600
USART2.IPParameters=VirtualMode-Asynchronous,BaudRate
USART2.VirtualMode-Asynchronous=VM_ASYNC
USART6.IPParameters=VirtualMode-Asynchronous
USART6.VirtualMode-Asynchronous=VM_ASYNC
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
