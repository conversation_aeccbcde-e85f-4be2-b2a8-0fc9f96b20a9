--cpu Cortex-M7.fp.dp
"defect detection cart\startup_stm32h723xx.o"
"defect detection cart\main.o"
"defect detection cart\gpio.o"
"defect detection cart\dma.o"
"defect detection cart\i2c.o"
"defect detection cart\tim.o"
"defect detection cart\usart.o"
"defect detection cart\stm32h7xx_it.o"
"defect detection cart\stm32h7xx_hal_msp.o"
"defect detection cart\stm32h7xx_hal_cortex.o"
"defect detection cart\stm32h7xx_hal_rcc.o"
"defect detection cart\stm32h7xx_hal_rcc_ex.o"
"defect detection cart\stm32h7xx_hal_flash.o"
"defect detection cart\stm32h7xx_hal_flash_ex.o"
"defect detection cart\stm32h7xx_hal_gpio.o"
"defect detection cart\stm32h7xx_hal_hsem.o"
"defect detection cart\stm32h7xx_hal_dma.o"
"defect detection cart\stm32h7xx_hal_dma_ex.o"
"defect detection cart\stm32h7xx_hal_mdma.o"
"defect detection cart\stm32h7xx_hal_pwr.o"
"defect detection cart\stm32h7xx_hal_pwr_ex.o"
"defect detection cart\stm32h7xx_hal.o"
"defect detection cart\stm32h7xx_hal_i2c.o"
"defect detection cart\stm32h7xx_hal_i2c_ex.o"
"defect detection cart\stm32h7xx_hal_exti.o"
"defect detection cart\stm32h7xx_hal_tim.o"
"defect detection cart\stm32h7xx_hal_tim_ex.o"
"defect detection cart\stm32h7xx_hal_uart.o"
"defect detection cart\stm32h7xx_hal_uart_ex.o"
"defect detection cart\system_stm32h7xx.o"
"defect detection cart\esp8266.o"
"defect detection cart\gps.o"
"defect detection cart\motor.o"
"defect detection cart\oled.o"
"defect detection cart\oled_font.o"
"defect detection cart\servo.o"
"defect detection cart\systick.o"
"defect detection cart\v831.o"
"defect detection cart\vl53l0x.o"
"defect detection cart\vl53l0x_api.o"
"defect detection cart\vl53l0x_api_calibration.o"
"defect detection cart\vl53l0x_api_core.o"
"defect detection cart\vl53l0x_api_ranging.o"
"defect detection cart\vl53l0x_api_strings.o"
"defect detection cart\vl53l0x_gen.o"
"defect detection cart\vl53l0x_i2c.o"
"defect detection cart\vl53l0x_platform.o"
--strict --scatter "Defect detection cart\Defect detection cart.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "Defect detection cart.map" -o "Defect detection cart\Defect detection cart.axf"