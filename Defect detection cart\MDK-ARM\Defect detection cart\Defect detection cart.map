Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32h723xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(STACK) for __initial_sp
    startup_stm32h723xx.o(RESET) refers to startup_stm32h723xx.o(.text) for Reset_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.I2C3_EV_IRQHandler) for I2C3_EV_IRQHandler
    startup_stm32h723xx.o(RESET) refers to stm32h7xx_it.o(i.I2C3_ER_IRQHandler) for I2C3_ER_IRQHandler
    startup_stm32h723xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32h723xx.o(.text) refers to system_stm32h7xx.o(i.ExitRun0Mode) for ExitRun0Mode
    startup_stm32h723xx.o(.text) refers to system_stm32h7xx.o(i.SystemInit) for SystemInit
    startup_stm32h723xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32h723xx.o(.text) refers to startup_stm32h723xx.o(HEAP) for Heap_Mem
    startup_stm32h723xx.o(.text) refers to startup_stm32h723xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) for HAL_PWREx_ConfigSupply
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable) for HAL_MPU_Disable
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion) for HAL_MPU_ConfigRegion
    main.o(i.main) refers to stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable) for HAL_MPU_Enable
    main.o(i.main) refers to stm32h7xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C3_Init) for MX_I2C3_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.main) refers to oled.o(i.OLED_Clear) for OLED_Clear
    main.o(i.main) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    main.o(i.main) refers to oled.o(i.OLED_Update) for OLED_Update
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig) for HAL_SYSCFG_AnalogSwitchConfig
    dma.o(i.MX_DMA_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    i2c.o(i.HAL_I2C_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    i2c.o(i.HAL_I2C_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspInit) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C1_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C3_Init) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C3_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter) for HAL_I2CEx_ConfigAnalogFilter
    i2c.o(i.MX_I2C3_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter) for HAL_I2CEx_ConfigDigitalFilter
    i2c.o(i.MX_I2C3_Init) refers to stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus) for HAL_I2CEx_EnableFastModePlus
    i2c.o(i.MX_I2C3_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM4_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) for HAL_RCCEx_PeriphCLKConfig
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART1_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART2_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) for HAL_UARTEx_SetTxFifoThreshold
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) for HAL_UARTEx_SetRxFifoThreshold
    usart.o(i.MX_USART6_UART_Init) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode) for HAL_UARTEx_DisableFifoMode
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler) refers to i2c.o(.bss) for hdma_i2c3_tx
    stm32h7xx_it.o(i.I2C3_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) for HAL_I2C_ER_IRQHandler
    stm32h7xx_it.o(i.I2C3_ER_IRQHandler) refers to i2c.o(.bss) for hi2c3
    stm32h7xx_it.o(i.I2C3_EV_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) for HAL_I2C_EV_IRQHandler
    stm32h7xx_it.o(i.I2C3_EV_IRQHandler) refers to i2c.o(.bss) for hi2c3
    stm32h7xx_it.o(i.SysTick_Handler) refers to stm32h7xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32h7xx_it.o(i.USART1_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32h7xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32h7xx_it.o(i.USART2_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32h7xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit) refers to stm32h7xx_hal.o(.data) for uwTickPrio
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig) refers to stm32h7xx_hal.o(i.HAL_GetREVID) for HAL_GetREVID
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback) for HAL_RCCEx_CRS_SyncOkCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback) for HAL_RCCEx_CRS_SyncWarnCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback) for HAL_RCCEx_CRS_ExpectedSyncCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback) for HAL_RCCEx_CRS_ErrorCallback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq) for HAL_RCCEx_GetPLL1ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback) for HAL_RCCEx_LSECSS_Callback
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) for RCCEx_PLL2_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) for RCCEx_PLL3_Config
    stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32h7xx_hal_flash.o(.bss) for .bss
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation) for FLASH_OB_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC) refers to stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation) for FLASH_CRC_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32h7xx_hal_flash.o(.bss) for pFlash
    stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler) refers to stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback) for HAL_HSEM_FreeCallback
    stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32h7xx_hal_dma.o(.constdata) for .constdata
    stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask) for DMA_CalcDMAMUXChannelBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask) for DMA_CalcDMAMUXRequestGenBaseAndMask
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32h7xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init) refers to stm32h7xx_hal_mdma.o(i.MDMA_Init) for MDMA_Init
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer) refers to stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort) for HAL_MDMA_Abort
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT) refers to stm32h7xx_hal_mdma.o(i.MDMA_SetConfig) for MDMA_SetConfig
    stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback) for HAL_PWREx_AVDCallback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback) for HAL_PWREx_WKUP1_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback) for HAL_PWREx_WKUP2_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback) for HAL_PWREx_WKUP4_Callback
    stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler) refers to stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback) for HAL_PWREx_WKUP6_Callback
    stm32h7xx_hal.o(i.HAL_DeInit) refers to stm32h7xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_Delay) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_GetTickPrio) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_IncTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_Init) refers to stm32h7xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.constdata) for D1CorePrescTable
    stm32h7xx_hal.o(i.HAL_Init) refers to system_stm32h7xx.o(.data) for SystemD2Clock
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32h7xx_hal.o(i.HAL_InitTick) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal.o(i.HAL_InitTick) refers to system_stm32h7xx.o(.data) for SystemCoreClock
    stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32h7xx_hal.o(i.HAL_SetTickFreq) refers to stm32h7xx_hal.o(.data) for .data
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) for I2C_Master_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) for I2C_DMAMasterReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) for I2C_DMAMasterTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) for I2C_Mem_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) for I2C_DMASlaveReceiveCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) for I2C_DMASlaveTransmitCplt
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_i2c.o(i.I2C_DMAError) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) for I2C_Master_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) for I2C_Slave_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) refers to stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) for I2C_Mem_ISR_DMA
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) for I2C_TreatErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32h7xx_hal_i2c.o(i.I2C_ITError) refers to stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) for I2C_Slave_ISR_IT
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt) for I2C_ITMasterSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ) for I2C_Disable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ) for I2C_Enable_IRQ
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt) for I2C_ITMasterCplt
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_TransferConfig) for I2C_TransferConfig
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) for I2C_WaitOnTXISFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt) for I2C_ITSlaveCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt) for I2C_ITListenCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR) for I2C_Flush_TXDR
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt) for I2C_ITAddrCplt
    stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT) refers to stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt) for I2C_ITSlaveSeqCplt
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred) for I2C_IsErrorOccurred
    stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback) for HAL_TIMEx_Break2Callback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig) for TIM_OC5_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig) for TIM_OC6_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAError) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback) for HAL_UARTEx_WakeupCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback) for HAL_UARTEx_TxFifoEmptyCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback) for HAL_UARTEx_RxFifoFullCallback
    stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart.o(i.HAL_UART_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT) for UART_TxISR_8BIT
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN) for UART_TxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN) for UART_TxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT) refers to stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT) for UART_TxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_CheckIdleState) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart.o(i.UART_DMAError) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to esp8266.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to esp8266.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to esp8266.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to esp8266.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to esp8266.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq) for HAL_RCCEx_GetD3PCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq) for HAL_RCCEx_GetPLL2ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq) for HAL_RCCEx_GetPLL3ClockFreq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32h7xx_hal_uart.o(i.UART_SetConfig) refers to stm32h7xx_hal_uart.o(.constdata) for .constdata
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32h7xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN) for UART_RxISR_8BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN) for UART_RxISR_16BIT_FIFOEN
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT) for UART_RxISR_8BIT
    stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) refers to stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT) for UART_RxISR_16BIT
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32h7xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig) for UART_AdvFeatureConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init) refers to stm32h7xx_hal_uart.o(i.UART_CheckIdleState) for UART_CheckIdleState
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold) refers to stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) for UARTEx_SetNbDataToProcess
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig) refers to stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess) refers to stm32h7xx_hal_uart_ex.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.constdata) for .constdata
    system_stm32h7xx.o(i.SystemCoreClockUpdate) refers to system_stm32h7xx.o(.data) for .data
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_dec.o(.text) for _printf_int_dec
    esp8266.o(i.ESP8266_ConnectAliyun) refers to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    esp8266.o(i.ESP8266_ConnectAliyun) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.ESP8266_ConnectAliyun) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.ESP8266_ConnectAliyun) refers to strlen.o(.text) for strlen
    esp8266.o(i.ESP8266_ConnectAliyun) refers to rt_memmove_v6.o(.text) for __aeabi_memmove
    esp8266.o(i.ESP8266_ConnectAliyun) refers to esp8266.o(i.ESP8266_SendCommand) for ESP8266_SendCommand
    esp8266.o(i.ESP8266_ConnectWiFi) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.ESP8266_ConnectWiFi) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.ESP8266_ConnectWiFi) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.ESP8266_ConnectWiFi) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.ESP8266_ConnectWiFi) refers to esp8266.o(i.ESP8266_SendCommand) for ESP8266_SendCommand
    esp8266.o(i.ESP8266_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    esp8266.o(i.ESP8266_Init) refers to esp8266.o(i.ESP8266_SendCommand) for ESP8266_SendCommand
    esp8266.o(i.ESP8266_Init) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.ESP8266_Init) refers to esp8266.o(.data) for .data
    esp8266.o(i.ESP8266_Init) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.ESP8266_PublishData) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    esp8266.o(i.ESP8266_PublishData) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    esp8266.o(i.ESP8266_PublishData) refers to _printf_str.o(.text) for _printf_str
    esp8266.o(i.ESP8266_PublishData) refers to __2snprintf.o(.text) for __2snprintf
    esp8266.o(i.ESP8266_PublishData) refers to esp8266.o(i.ESP8266_SendCommand) for ESP8266_SendCommand
    esp8266.o(i.ESP8266_PublishData) refers to esp8266.o(.conststring) for .conststring
    esp8266.o(i.ESP8266_SendCommand) refers to rt_memclr.o(.text) for __aeabi_memclr
    esp8266.o(i.ESP8266_SendCommand) refers to strlen.o(.text) for strlen
    esp8266.o(i.ESP8266_SendCommand) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    esp8266.o(i.ESP8266_SendCommand) refers to stm32h7xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    esp8266.o(i.ESP8266_SendCommand) refers to strncmp.o(.text) for strncmp
    esp8266.o(i.ESP8266_SendCommand) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    esp8266.o(i.ESP8266_SendCommand) refers to esp8266.o(.data) for .data
    esp8266.o(i.ESP8266_SendCommand) refers to esp8266.o(.bss) for .bss
    esp8266.o(i.HAL_UART_RxCpltCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    esp8266.o(i.HAL_UART_RxCpltCallback) refers to esp8266.o(.data) for .data
    esp8266.o(i.HAL_UART_RxCpltCallback) refers to esp8266.o(.bss) for .bss
    gps.o(i.GPS_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    gps.o(i.GPS_Init) refers to gps.o(.data) for .data
    gps.o(i.GPS_Init) refers to gps.o(.bss) for .bss
    gps.o(i.GPS_Process) refers to strstr.o(.text) for strstr
    gps.o(i.GPS_Process) refers to strtok.o(.text) for strtok
    gps.o(i.GPS_Process) refers to strlen.o(.text) for strlen
    gps.o(i.GPS_Process) refers to strncpy.o(.text) for strncpy
    gps.o(i.GPS_Process) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    gps.o(i.GPS_Process) refers to gps.o(i.GPS_DMtoD) for GPS_DMtoD
    gps.o(i.GPS_Process) refers to atoi.o(.text) for atoi
    gps.o(i.GPS_Process) refers to gps.o(.bss) for .bss
    gps.o(i.GPS_Process) refers to gps.o(.data) for .data
    gps.o(i.HAL_UART2_RxCpltCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    gps.o(i.HAL_UART2_RxCpltCallback) refers to gps.o(.data) for .data
    gps.o(i.HAL_UART2_RxCpltCallback) refers to gps.o(.bss) for .bss
    motor.o(i.Encoder_GetCount) refers to motor.o(.data) for .data
    motor.o(i.Encoder_GetDistanceMM) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    motor.o(i.Encoder_Init) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Reset) refers to motor.o(.data) for .data
    motor.o(i.Encoder_Update) refers to motor.o(.data) for .data
    motor.o(i.Motor_Control) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ClearArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_IsInAngle) for OLED_IsInAngle
    oled.o(i.OLED_DrawArc) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawEllipse) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_DrawRectangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawLine) for OLED_DrawLine
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_pnpoly) for OLED_pnpoly
    oled.o(i.OLED_DrawTriangle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_GetPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_GPIO_Init) for OLED_GPIO_Init
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Update) for OLED_Update
    oled.o(i.OLED_IsInAngle) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    oled.o(i.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(i.OLED_Printf) refers to oled.o(i.OLED_ShowString) for OLED_ShowString
    oled.o(i.OLED_Reverse) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ReverseArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_SetCursor) refers to oled.o(i.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowBinNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChar) refers to oled_font.o(.constdata) for OLED_F6x8
    oled.o(i.OLED_ShowChar) refers to oled_font.o(.constdata) for OLED_F8x16
    oled.o(i.OLED_ShowChinese) refers to strcmpv7m_pel.o(.text) for strcmp
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_ShowImage) for OLED_ShowImage
    oled.o(i.OLED_ShowChinese) refers to oled_font.o(.constdata) for OLED_CF16x16
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowFloatNum) refers to round.o(i.__hardfp_round) for __hardfp_round
    oled.o(i.OLED_ShowFloatNum) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowHexNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowImage) refers to oled.o(i.OLED_ClearArea) for OLED_ClearArea
    oled.o(i.OLED_ShowImage) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowSignedNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_Update) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_Update) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_SetCursor) for OLED_SetCursor
    oled.o(i.OLED_UpdateArea) refers to oled.o(i.OLED_WriteData) for OLED_WriteData
    oled.o(i.OLED_UpdateArea) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_WriteCommand) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteCommand) refers to i2c.o(.bss) for hi2c3
    oled.o(i.OLED_WriteData) refers to h1_alloc.o(.text) for malloc
    oled.o(i.OLED_WriteData) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    oled.o(i.OLED_WriteData) refers to h1_free.o(.text) for free
    oled.o(i.OLED_WriteData) refers to i2c.o(.bss) for hi2c3
    servo.o(i.SERVO_Clamp) refers to servo.o(i.SERVO_SetSpeed) for SERVO_SetSpeed
    servo.o(i.SERVO_Init) refers to stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    servo.o(i.SERVO_Init) refers to servo.o(i.SERVO_SetPulse) for SERVO_SetPulse
    servo.o(i.SERVO_Init) refers to servo.o(.data) for .data
    servo.o(i.SERVO_Release) refers to servo.o(i.SERVO_SetSpeed) for SERVO_SetSpeed
    servo.o(i.SERVO_SetPulse) refers to servo.o(.data) for .data
    servo.o(i.SERVO_SetSpeed) refers to servo.o(i.SERVO_SetPulse) for SERVO_SetPulse
    servo.o(i.SERVO_Stop) refers to servo.o(i.SERVO_SetPulse) for SERVO_SetPulse
    v831.o(i.HAL_UART6_RxCpltCallback) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    v831.o(i.HAL_UART6_RxCpltCallback) refers to v831.o(.data) for .data
    v831.o(i.HAL_UART6_RxCpltCallback) refers to v831.o(.bss) for .bss
    v831.o(i.ProcessByte) refers to v831.o(.data) for .data
    v831.o(i.ProcessByte) refers to v831.o(.bss) for .bss
    v831.o(i.V831_Init) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    v831.o(i.V831_Init) refers to v831.o(.data) for .data
    v831.o(i.V831_Init) refers to v831.o(.bss) for .bss
    v831.o(i.V831_ProcessData) refers to v831.o(i.ProcessByte) for ProcessByte
    v831.o(i.V831_ProcessData) refers to v831.o(.data) for .data
    v831.o(i.V831_ProcessData) refers to v831.o(.bss) for .bss
    v831.o(i.V831_RequestDetection) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    v831.o(i.V831_SendHeartbeat) refers to stm32h7xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    vl53l0x.o(i.print_pal_error) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vl53l0x.o(i.print_pal_error) refers to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vl53l0x.o(i.print_pal_error) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vl53l0x.o(i.print_pal_error) refers to _printf_dec.o(.text) for _printf_int_dec
    vl53l0x.o(i.print_pal_error) refers to _printf_str.o(.text) for _printf_str
    vl53l0x.o(i.print_pal_error) refers to vl53l0x_api.o(i.VL53L0X_GetPalErrorString) for VL53L0X_GetPalErrorString
    vl53l0x.o(i.print_pal_error) refers to __2printf.o(.text) for __2printf
    vl53l0x.o(i.vl53l0x_Addr_set) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vl53l0x.o(i.vl53l0x_Addr_set) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vl53l0x.o(i.vl53l0x_Addr_set) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vl53l0x.o(i.vl53l0x_Addr_set) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x.o(i.vl53l0x_Addr_set) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x.o(i.vl53l0x_Addr_set) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceAddress) for VL53L0X_SetDeviceAddress
    vl53l0x.o(i.vl53l0x_Addr_set) refers to vl53l0x.o(i.print_pal_error) for print_pal_error
    vl53l0x.o(i.vl53l0x_Addr_set) refers to __2printf.o(.text) for __2printf
    vl53l0x.o(i.vl53l0x_info) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vl53l0x.o(i.vl53l0x_info) refers to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vl53l0x.o(i.vl53l0x_info) refers to _printf_str.o(.text) for _printf_str
    vl53l0x.o(i.vl53l0x_info) refers to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vl53l0x.o(i.vl53l0x_info) refers to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vl53l0x.o(i.vl53l0x_info) refers to __2printf.o(.text) for __2printf
    vl53l0x.o(i.vl53l0x_info) refers to vl53l0x.o(.bss) for .bss
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x_i2c.o(i.VL53L0X_i2c_init) for VL53L0X_i2c_init
    vl53l0x.o(i.vl53l0x_init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    vl53l0x.o(i.vl53l0x_init) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.vl53l0x_Addr_set) for vl53l0x_Addr_set
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x_api.o(i.VL53L0X_DataInit) for VL53L0X_DataInit
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x_api.o(i.VL53L0X_GetDeviceInfo) for VL53L0X_GetDeviceInfo
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(i.print_pal_error) for print_pal_error
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(.bss) for .bss
    vl53l0x.o(i.vl53l0x_init) refers to vl53l0x.o(.data) for .data
    vl53l0x.o(i.vl53l0x_reset) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    vl53l0x.o(i.vl53l0x_reset) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x.o(i.vl53l0x_reset) refers to vl53l0x.o(i.vl53l0x_Addr_set) for vl53l0x_Addr_set
    vl53l0x.o(i.vl53l0x_reset) refers to vl53l0x_api.o(i.VL53L0X_DataInit) for VL53L0X_DataInit
    vl53l0x.o(i.vl53l0x_test) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x.o(i.vl53l0x_test) refers to vl53l0x.o(i.vl53l0x_init) for vl53l0x_init
    vl53l0x.o(i.vl53l0x_test) refers to vl53l0x_gen.o(i.vl53l0x_general_test) for vl53l0x_general_test
    vl53l0x.o(i.vl53l0x_test) refers to vl53l0x.o(.bss) for .bss
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds) for VL53L0X_GetInterruptThresholds
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) for VL53L0X_load_tuning_settings
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) refers to vl53l0x_api.o(.data) for .data
    vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_platform.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) for VL53L0X_GetDeviceParameters
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_DataInit) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) for VL53L0X_SetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_GetDeviceErrorStatus) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetDeviceErrorString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string) for VL53L0X_get_device_error_string
    vl53l0x_api.o(i.VL53L0X_GetDeviceInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_device_info) for VL53L0X_get_device_info
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) for VL53L0X_GetInterMeasurementPeriodMilliSeconds
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps) for VL53L0X_GetXTalkCompensationRateMegaCps
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) for VL53L0X_GetOffsetCalibrationDataMicroMeter
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) for VL53L0X_GetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable) for VL53L0X_GetWrapAroundCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds) for VL53L0X_GetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_GetFractionEnable) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetGpioConfig) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_platform.o(i.VL53L0X_RdDWord) for VL53L0X_RdDWord
    vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckCurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info) for VL53L0X_get_limit_check_info
    vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) refers to vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus) for VL53L0X_GetInterruptMaskStatus
    vl53l0x_api.o(i.VL53L0X_GetMeasurementRefSignal) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds) refers to vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) for VL53L0X_get_measurement_timing_budget_micro_seconds
    vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter) for VL53L0X_get_offset_calibration_data_micro_meter
    vl53l0x_api.o(i.VL53L0X_GetPalErrorString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string) for VL53L0X_get_pal_error_string
    vl53l0x_api.o(i.VL53L0X_GetPalStateString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string) for VL53L0X_get_pal_state_string
    vl53l0x_api.o(i.VL53L0X_GetPowerMode) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetProductRevision) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetRangeStatusString) refers to vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string) for VL53L0X_get_range_status_string
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to vl53l0x_platform.o(i.VL53L0X_ReadMulti) for VL53L0X_ReadMulti
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) for VL53L0X_get_pal_range_status
    vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration) for VL53L0X_get_ref_calibration
    vl53l0x_api.o(i.VL53L0X_GetReferenceSpads) refers to vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) for VL53L0X_get_reference_spads
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) refers to vl53l0x_api.o(i.sequence_step_enabled) for sequence_step_enabled
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) refers to vl53l0x_api.o(i.sequence_step_enabled) for sequence_step_enabled
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_GetSequenceStepsInfo) refers to vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info) for VL53L0X_get_sequence_steps_info
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) for VL53L0X_get_total_signal_rate
    vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) refers to vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) for VL53L0X_get_vcsel_pulse_period
    vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_PerformOffsetCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) for VL53L0X_perform_offset_calibration
    vl53l0x_api.o(i.VL53L0X_PerformRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) for VL53L0X_perform_ref_calibration
    vl53l0x_api.o(i.VL53L0X_PerformRefSpadManagement) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) for VL53L0X_perform_ref_spad_management
    vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) refers to vl53l0x_api.o(i.VL53L0X_StartMeasurement) for VL53L0X_StartMeasurement
    vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) refers to vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) for VL53L0X_measurement_poll_for_completion
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement) for VL53L0X_PerformSingleMeasurement
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData) for VL53L0X_GetRangingMeasurementData
    vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api.o(i.VL53L0X_PerformXTalkCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) for VL53L0X_perform_xtalk_calibration
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_ResetDevice) refers to vl53l0x_platform.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api.o(i.VL53L0X_SetDeviceAddress) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) for VL53L0X_SetInterMeasurementPeriodMilliSeconds
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) for VL53L0X_SetXTalkCompensationRateMegaCps
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) for VL53L0X_SetLimitCheckValue
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) for VL53L0X_SetWrapAroundCheckEnable
    vl53l0x_api.o(i.VL53L0X_SetDeviceParameters) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetDmaxCalParameters) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_platform.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api.o(i.VL53L0X_SetGpioConfig) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds) refers to vl53l0x_platform.o(i.VL53L0X_WrDWord) for VL53L0X_WrDWord
    vl53l0x_api.o(i.VL53L0X_SetInterruptThresholds) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) refers to vl53l0x_platform.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetLinearityCorrectiveGain) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) refers to vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) for VL53L0X_set_measurement_timing_budget_micro_seconds
    vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter) for VL53L0X_set_offset_calibration_data_micro_meter
    vl53l0x_api.o(i.VL53L0X_SetPowerMode) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetPowerMode) refers to vl53l0x_api.o(i.VL53L0X_StaticInit) for VL53L0X_StaticInit
    vl53l0x_api.o(i.VL53L0X_SetRangeFractionEnable) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetRefCalibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration) for VL53L0X_set_ref_calibration
    vl53l0x_api.o(i.VL53L0X_SetReferenceSpads) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) for VL53L0X_set_reference_spads
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperFactor) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetVcselPulsePeriod) refers to vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) for VL53L0X_set_vcsel_pulse_period
    vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_StartMeasurement) refers to vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) for VL53L0X_CheckAndLoadInterruptSettings
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) for VL53L0X_perform_ref_spad_management
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) for VL53L0X_set_reference_spads
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) for VL53L0X_load_tuning_settings
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_SetGpioConfig) for VL53L0X_SetGpioConfig
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetDeviceParameters) for VL53L0X_GetDeviceParameters
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetFractionEnable) for VL53L0X_GetFractionEnable
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) for VL53L0X_SetSequenceStepEnable
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api.o(i.VL53L0X_StaticInit) refers to vl53l0x_api.o(.data) for .data
    vl53l0x_api.o(i.VL53L0X_StopMeasurement) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api.o(i.VL53L0X_StopMeasurement) refers to vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings) for VL53L0X_CheckAndLoadInterruptSettings
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter) for VL53L0X_GetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) refers to vl53l0x_api_calibration.o(i.get_ref_spad_map) for get_ref_spad_map
    vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads) refers to vl53l0x_api_calibration.o(i.count_enabled_spads) for count_enabled_spads
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable) for VL53L0X_GetSequenceStepEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable) for VL53L0X_SetSequenceStepEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) for VL53L0X_perform_single_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) for VL53L0X_perform_vhv_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) for VL53L0X_perform_phase_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration) for VL53L0X_perform_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.enable_ref_spads) for enable_ref_spads
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) for perform_ref_signal_measurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.get_next_good_spad) for get_next_good_spad
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.enable_spad_bit) for enable_spad_bit
    vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management) refers to vl53l0x_api_calibration.o(i.set_ref_spad_map) for set_ref_spad_map
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) for VL53L0X_measurement_poll_for_completion
    vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) refers to vl53l0x_api.o(i.VL53L0X_ClearInterruptMask) for VL53L0X_ClearInterruptMask
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration) for VL53L0X_perform_single_ref_calibration
    vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable) for VL53L0X_SetXTalkCompensationEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) for VL53L0X_SetXTalkCompensationRateMegaCps
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) refers to vl53l0x_platform.o(i.VL53L0X_UpdateByte) for VL53L0X_UpdateByte
    vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration) refers to vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io) for VL53L0X_ref_calibration_io
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads) refers to vl53l0x_api_calibration.o(i.enable_ref_spads) for enable_ref_spads
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.get_next_good_spad) for get_next_good_spad
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.is_aperture) for is_aperture
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.enable_spad_bit) for enable_spad_bit
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.set_ref_spad_map) for set_ref_spad_map
    vl53l0x_api_calibration.o(i.enable_ref_spads) refers to vl53l0x_api_calibration.o(i.get_ref_spad_map) for get_ref_spad_map
    vl53l0x_api_calibration.o(i.get_ref_spad_map) refers to vl53l0x_platform.o(i.VL53L0X_ReadMulti) for VL53L0X_ReadMulti
    vl53l0x_api_calibration.o(i.is_aperture) refers to vl53l0x_api_calibration.o(.data) for .data
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_api_calibration.o(i.perform_ref_signal_measurement) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_calibration.o(i.set_ref_spad_map) refers to vl53l0x_platform.o(i.VL53L0X_WriteMulti) for VL53L0X_WriteMulti
    vl53l0x_api_core.o(i.VL53L0X_calc_dmax) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) for VL53L0X_get_total_signal_rate
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) for VL53L0X_get_total_xtalk_rate
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) for VL53L0X_calc_timeout_mclks
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) refers to vl53l0x_api_core.o(i.VL53L0X_calc_dmax) for VL53L0X_calc_dmax
    vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) refers to vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps) for VL53L0X_calc_macro_period_ps
    vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us) refers to vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps) for VL53L0X_calc_macro_period_ps
    vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_platform.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_api_core.o(i.VL53L0X_device_read_strobe) for VL53L0X_device_read_strobe
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to vl53l0x_platform.o(i.VL53L0X_RdDWord) for VL53L0X_RdDWord
    vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable) for VL53L0X_GetLimitCheckEnable
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate) for VL53L0X_calc_sigma_estimate
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue) for VL53L0X_GetLimitCheckValue
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate) refers to vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) for VL53L0X_get_total_xtalk_rate
    vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate) refers to vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable) for VL53L0X_GetXTalkCompensationEnable
    vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period) for VL53L0X_decode_vcsel_period
    vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings) refers to vl53l0x_platform.o(i.VL53L0X_WriteMulti) for VL53L0X_WriteMulti
    vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) refers to vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady) for VL53L0X_GetMeasurementDataReady
    vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion) refers to vl53l0x_platform.o(i.VL53L0X_PollingDelay) for VL53L0X_PollingDelay
    vl53l0x_api_core.o(i.VL53L0X_quadrature_sum) refers to vl53l0x_api_core.o(i.VL53L0X_isqrt) for VL53L0X_isqrt
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.VL53L0X_encode_vcsel_period) for VL53L0X_encode_vcsel_period
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.get_sequence_step_timeout) for get_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_core.o(i.set_sequence_step_timeout) for set_sequence_step_timeout
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period) refers to vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration) for VL53L0X_perform_phase_calibration
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_decode_timeout) for VL53L0X_decode_timeout
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.get_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us) for VL53L0X_calc_timeout_us
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod) for VL53L0X_GetVcselPulsePeriod
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks) for VL53L0X_calc_timeout_mclks
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_platform.o(i.VL53L0X_WrByte) for VL53L0X_WrByte
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_encode_timeout) for VL53L0X_encode_timeout
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_platform.o(i.VL53L0X_WrWord) for VL53L0X_WrWord
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables) for VL53L0X_GetSequenceStepEnables
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_platform.o(i.VL53L0X_RdWord) for VL53L0X_RdWord
    vl53l0x_api_core.o(i.set_sequence_step_timeout) refers to vl53l0x_api_core.o(i.VL53L0X_decode_timeout) for VL53L0X_decode_timeout
    vl53l0x_api_strings.o(i.VL53L0X_check_part_used) refers to vl53l0x_api_core.o(i.VL53L0X_get_info_from_device) for VL53L0X_get_info_from_device
    vl53l0x_api_strings.o(i.VL53L0X_check_part_used) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to vl53l0x_api_strings.o(i.VL53L0X_check_part_used) for VL53L0X_check_part_used
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_device_info) refers to vl53l0x_platform.o(i.VL53L0X_RdByte) for VL53L0X_RdByte
    vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string) refers to strcpy.o(.text) for strcpy
    vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info) refers to strcpy.o(.text) for strcpy
    vl53l0x_gen.o(i.delay_ms) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x_gen.o(i.vl53l0x_general_start) refers to vl53l0x_gen.o(i.vl53l0x_set_mode) for vl53l0x_set_mode
    vl53l0x_gen.o(i.vl53l0x_general_start) refers to vl53l0x_gen.o(i.vl53l0x_start_single_test) for vl53l0x_start_single_test
    vl53l0x_gen.o(i.vl53l0x_general_start) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    vl53l0x_gen.o(i.vl53l0x_general_start) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x_gen.o(i.vl53l0x_general_start) refers to vl53l0x_gen.o(.bss) for .bss
    vl53l0x_gen.o(i.vl53l0x_general_test) refers to vl53l0x_gen.o(i.vl53l0x_general_start) for vl53l0x_general_start
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x.o(i.vl53l0x_reset) for vl53l0x_reset
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_StaticInit) for VL53L0X_StaticInit
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetReferenceSpads) for VL53L0X_SetReferenceSpads
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to stm32h7xx_hal.o(i.HAL_Delay) for HAL_Delay
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetRefCalibration) for VL53L0X_SetRefCalibration
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter) for VL53L0X_SetOffsetCalibrationDataMicroMeter
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps) for VL53L0X_SetXTalkCompensationRateMegaCps
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_PerformRefCalibration) for VL53L0X_PerformRefCalibration
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_PerformRefSpadManagement) for VL53L0X_PerformRefSpadManagement
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetDeviceMode) for VL53L0X_SetDeviceMode
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable) for VL53L0X_SetLimitCheckEnable
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue) for VL53L0X_SetLimitCheckValue
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds) for VL53L0X_SetMeasurementTimingBudgetMicroSeconds
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x_api.o(i.VL53L0X_SetVcselPulsePeriod) for VL53L0X_SetVcselPulsePeriod
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x.o(i.print_pal_error) for print_pal_error
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x.o(.data) for AjustOK
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x.o(.bss) for Vl53l0x_data
    vl53l0x_gen.o(i.vl53l0x_set_mode) refers to vl53l0x.o(.data) for Mode_data
    vl53l0x_gen.o(i.vl53l0x_start_single_test) refers to vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement) for VL53L0X_PerformSingleRangingMeasurement
    vl53l0x_gen.o(i.vl53l0x_start_single_test) refers to rt_memclr.o(.text) for __aeabi_memclr
    vl53l0x_gen.o(i.vl53l0x_start_single_test) refers to vl53l0x_api.o(i.VL53L0X_GetRangeStatusString) for VL53L0X_GetRangeStatusString
    vl53l0x_gen.o(i.vl53l0x_start_single_test) refers to vl53l0x_gen.o(.data) for .data
    vl53l0x_i2c.o(i.VL53L0X_i2c_init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    vl53l0x_i2c.o(i.VL53L0X_i2c_init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    vl53l0x_i2c.o(i.VL53L0X_i2c_init) refers to stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    vl53l0x_i2c.o(i.VL53L0X_read_byte) refers to vl53l0x_i2c.o(i.VL_IIC_Read_nByte) for VL_IIC_Read_nByte
    vl53l0x_i2c.o(i.VL53L0X_read_dword) refers to vl53l0x_i2c.o(i.VL53L0X_read_multi) for VL53L0X_read_multi
    vl53l0x_i2c.o(i.VL53L0X_read_multi) refers to vl53l0x_i2c.o(i.VL_IIC_Read_nByte) for VL_IIC_Read_nByte
    vl53l0x_i2c.o(i.VL53L0X_read_word) refers to vl53l0x_i2c.o(i.VL53L0X_read_multi) for VL53L0X_read_multi
    vl53l0x_i2c.o(i.VL53L0X_write_byte) refers to vl53l0x_i2c.o(i.VL_IIC_Write_nByte) for VL_IIC_Write_nByte
    vl53l0x_i2c.o(i.VL53L0X_write_dword) refers to vl53l0x_i2c.o(i.VL_IIC_Write_nByte) for VL_IIC_Write_nByte
    vl53l0x_i2c.o(i.VL53L0X_write_multi) refers to vl53l0x_i2c.o(i.VL_IIC_Write_nByte) for VL_IIC_Write_nByte
    vl53l0x_i2c.o(i.VL53L0X_write_word) refers to vl53l0x_i2c.o(i.VL_IIC_Write_nByte) for VL_IIC_Write_nByte
    vl53l0x_i2c.o(i.VL_IIC_Read_nByte) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    vl53l0x_i2c.o(i.VL_IIC_Read_nByte) refers to i2c.o(.bss) for hi2c1
    vl53l0x_i2c.o(i.VL_IIC_Write_nByte) refers to stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    vl53l0x_i2c.o(i.VL_IIC_Write_nByte) refers to i2c.o(.bss) for hi2c1
    vl53l0x_platform.o(i.VL53L0X_RdByte) refers to vl53l0x_i2c.o(i.VL53L0X_read_byte) for VL53L0X_read_byte
    vl53l0x_platform.o(i.VL53L0X_RdDWord) refers to vl53l0x_i2c.o(i.VL53L0X_read_dword) for VL53L0X_read_dword
    vl53l0x_platform.o(i.VL53L0X_RdWord) refers to vl53l0x_i2c.o(i.VL53L0X_read_word) for VL53L0X_read_word
    vl53l0x_platform.o(i.VL53L0X_ReadMulti) refers to vl53l0x_i2c.o(i.VL53L0X_read_multi) for VL53L0X_read_multi
    vl53l0x_platform.o(i.VL53L0X_UpdateByte) refers to vl53l0x_i2c.o(i.VL53L0X_read_byte) for VL53L0X_read_byte
    vl53l0x_platform.o(i.VL53L0X_UpdateByte) refers to vl53l0x_i2c.o(i.VL53L0X_write_byte) for VL53L0X_write_byte
    vl53l0x_platform.o(i.VL53L0X_WrByte) refers to vl53l0x_i2c.o(i.VL53L0X_write_byte) for VL53L0X_write_byte
    vl53l0x_platform.o(i.VL53L0X_WrDWord) refers to vl53l0x_i2c.o(i.VL53L0X_write_dword) for VL53L0X_write_dword
    vl53l0x_platform.o(i.VL53L0X_WrWord) refers to vl53l0x_i2c.o(i.VL53L0X_write_word) for VL53L0X_write_word
    vl53l0x_platform.o(i.VL53L0X_WriteMulti) refers to vl53l0x_i2c.o(i.VL53L0X_write_multi) for VL53L0X_write_multi
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to stdio_streams.o(.bss) for __stdout
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _sputc.o(.text) for _sputc
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    strtok.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    strtok.o(.text) refers to strtok.o(.data) for .data
    rt_memmove_v6.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    rt_memmove_v6.o(.text) refers to rt_memmove_w.o(.text) for __memmove_aligned
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    atan2.o(i.__hardfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    round.o(i.__hardfp_round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.__hardfp_round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to fputc.o(i.fputc) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.bss) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    stdio_streams.o(.data) refers (Special) to initio.o(.text) for _initio
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    rt_memmove_w.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000024) refers (Weak) to initio.o(.text) for _initio
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__hardfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fputc.o(i.fputc) refers to flsbuf.o(.text) for __flsbuf_byte
    initio.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio.o(.text) refers to fopen.o(.text) for freopen
    initio.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio.o(.text) refers to h1_free.o(.text) for free
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    initio_locked.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000024) for __rt_lib_init_stdio_2
    initio_locked.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) for __rt_lib_shutdown_stdio_2
    initio_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    initio_locked.o(.text) refers to fopen.o(.text) for freopen
    initio_locked.o(.text) refers to defsig_rtred_outer.o(.text) for __rt_SIGRTRED
    initio_locked.o(.text) refers to setvbuf.o(.text) for setvbuf
    initio_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    initio_locked.o(.text) refers to h1_free.o(.text) for free
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stdout
    initio_locked.o(.text) refers to stdio_streams.o(.bss) for __stderr
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdin
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stdout
    initio_locked.o(.text) refers to stdio_streams.o(.data) for __aeabi_stderr
    initio_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdin_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stdout_name
    initio_locked.o(.text) refers to sys_io.o(.constdata) for __stderr_name
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_io.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.text) refers to strlen.o(.text) for strlen
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_io.o(.constdata) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_io.o(.constdata) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32h723xx.o(.text) for __user_initial_stackheap
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    flsbuf.o(.text) refers to stdio.o(.text) for _deferredlazyseek
    flsbuf.o(.text) refers to sys_io.o(.text) for _sys_flen
    flsbuf.o(.text) refers to h1_alloc.o(.text) for malloc
    streamlock.o(.data) refers (Special) to initio.o(.text) for _initio
    fopen.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen.o(.text) refers to fseek.o(.text) for _fseek
    fopen.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fclose.o(.text) refers to stdio.o(.text) for _fflush
    fclose.o(.text) refers to sys_io.o(.text) for _sys_close
    fclose.o(.text) refers to h1_free.o(.text) for free
    fclose.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to fclose.o(.text) for _fclose_internal
    fopen_locked.o(.text) refers to sys_io.o(.text) for _sys_open
    fopen_locked.o(.text) refers to fseek.o(.text) for _fseek
    fopen_locked.o(.text) refers to h1_alloc.o(.text) for malloc
    fopen_locked.o(.text) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    fopen_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fopen_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m_pel.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtred_outer.o(.text) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig_rtred_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtred_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libshutdown2.o(.ARM.Collect$$libshutdown$$00000005) refers (Weak) to initio.o(.text) for _terminateio
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    flsbuf_fwide.o(.text) refers to flsbuf.o(.text) for __flsbuf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    _printf_char_file_locked.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file_locked.o(.text) refers to fputc.o(i._fputc$unlocked) for _fputc$unlocked
    fseek.o(.text) refers to sys_io.o(.text) for _sys_istty
    fseek.o(.text) refers to ftell.o(.text) for _ftell_internal
    fseek.o(.text) refers to stdio.o(.text) for _seterr
    stdio.o(.text) refers to sys_io.o(.text) for _sys_seek
    fwritefast.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    fwritefast_locked.o(.text) refers to stdio.o(.text) for _writebuf
    fwritefast_locked.o(.text) refers to flsbuf.o(.text) for __flsbuf_byte
    fwritefast_locked.o(.text) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    assert_stdio.o(.text) refers to fputs.o(.text) for fputs
    assert_stdio.o(.text) refers to fflush.o(.text) for fflush
    assert_stdio.o(.text) refers to stdio_streams.o(.bss) for __stderr
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    fflush.o(.text) refers to stdio.o(.text) for _fflush
    fflush.o(.text) refers to fseek.o(.text) for _fseek
    fflush.o(.text) refers to stdio_streams.o(.bss) for __stdin
    fputs.o(.text) refers to fputc.o(i.fputc) for fputc
    ftell.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    fflush_locked.o(.text) refers to stdio.o(.text) for _fflush
    fflush_locked.o(.text) refers to fseek.o(.text) for _fseek
    fflush_locked.o(.text) refers to fflush.o(.text) for _do_fflush
    fflush_locked.o(.text) refers to streamlock.o(.data) for _stream_list_lock
    fflush_locked.o(.text) refers to stdio_streams.o(.bss) for __stdin
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtred_inner.o(.text) for __rt_SIGRTRED_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__softfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__softfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__softfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__support_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__support_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__support_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (124 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (44 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (128 bytes).
    Removing stm32h7xx_it.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_it.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_it.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_GetCurrentCPUID), (4 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32h7xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DeInit), (496 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (16 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (92 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (260 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (148 bytes).
    Removing stm32h7xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSConfig), (108 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSGetSynchronizationInfo), (40 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSSoftwareSynchronizationGenerate), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRSWaitSynchronization), (136 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_ExpectedSyncCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_IRQHandler), (116 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncOkCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_CRS_SyncWarnCallback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_DisableLSECSS), (28 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_EnableLSECSS_IT), (52 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1PCLK1Freq), (36 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD1SysClockFreq), (68 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL1ClockFreq), (328 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (428 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (668 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_KerWakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_Callback), (2 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_LSECSS_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WWDGxSysResetConfig), (16 bytes).
    Removing stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_WakeUpStopCLKConfig), (20 bytes).
    Removing stm32h7xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_CRC_WaitForLastOperation), (112 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_OB_WaitForLastOperation), (80 bytes).
    Removing stm32h7xx_hal_flash.o(i.FLASH_WaitForLastOperation), (120 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (204 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program), (148 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Program_IT), (128 bytes).
    Removing stm32h7xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32h7xx_hal_flash.o(.bss), (28 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_MassErase), (36 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (160 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_ComputeCRC), (188 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (196 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (144 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Lock_Bank1), (20 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (184 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (308 bytes).
    Removing stm32h7xx_hal_flash_ex.o(i.HAL_FLASHEx_Unlock_Bank1), (44 bytes).
    Removing stm32h7xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_DeInit), (352 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32h7xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_hsem.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ActivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_DeactivateNotification), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FastTake), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_FreeCallback), (2 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_GetClearKey), (12 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IRQHandler), (32 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_IsSemTaken), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Release), (20 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_ReleaseAll), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_SetClearKey), (16 bytes).
    Removing stm32h7xx_hal_hsem.o(i.HAL_HSEM_Take), (40 bytes).
    Removing stm32h7xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma.o(i.DMA_SetConfig), (544 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_DeInit), (488 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (1118 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (90 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start), (368 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_Start_IT), (668 bytes).
    Removing stm32h7xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (160 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (144 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxRequestGenerator), (176 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_ConfigMuxSync), (216 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_DisableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_EnableMuxRequestGenerator), (26 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MUX_IRQHandler), (100 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (640 bytes).
    Removing stm32h7xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (1128 bytes).
    Removing stm32h7xx_hal_mdma.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort), (116 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Abort_IT), (38 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_ConfigPostRequestMask), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_DeInit), (86 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GenerateSWRequest), (52 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetError), (4 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_GetState), (6 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_IRQHandler), (376 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Init), (94 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_AddNode), (208 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_CreateNode), (268 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_DisableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_EnableCircularMode), (78 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_LinkedList_RemoveNode), (188 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_PollForTransfer), (246 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_RegisterCallback), (88 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start), (102 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_Start_IT), (154 bytes).
    Removing stm32h7xx_hal_mdma.o(i.HAL_MDMA_UnRegisterCallback), (104 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_Init), (176 bytes).
    Removing stm32h7xx_hal_mdma.o(i.MDMA_SetConfig), (128 bytes).
    Removing stm32h7xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (136 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DeInit), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (20 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (28 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (48 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (80 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (34 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_AVDCallback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearPendingEvent), (4 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ClearWakeupFlag), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigAVD), (132 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigD3Domain), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlStopModeVoltageScaling), (20 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (72 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBatteryCharging), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_DisableWakeUpPin), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableAVD), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBatteryCharging), (28 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableMonitoring), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBReg), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableUSBVoltageDetector), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnableWakeUpPin), (136 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTANDBYMode), (56 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_EnterSTOPMode), (100 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetStopModeVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetSupplyConfig), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetTemperatureLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVBATLevel), (32 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_GetWakeupFlag), (12 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_PVD_AVD_IRQHandler), (96 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WAKEUP_PIN_IRQHandler), (84 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP1_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP2_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP4_Callback), (2 bytes).
    Removing stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_WKUP6_Callback), (2 bytes).
    Removing stm32h7xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DeInit), (124 bytes).
    Removing stm32h7xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_DisableDomain3DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_ClearFlag), (22 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D1_EventInputConfig), (66 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_D3_EventInputConfig), (70 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_EdgeConfig), (58 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EXTI_GenerateSWInterrupt), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableCompensationCell), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStandbyMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_EnableDomain3DBGStopMode), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetFMCMemorySwappingConfig), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32h7xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ADC2ALT_Rout0Config), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ADC2ALT_Rout1Config), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CM7BootAddConfig), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeConfig), (24 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_CompensationCodeSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_DisableVREFBUF), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_ETHInterfaceSelect), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableBOOST), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableIOSpeedOptimize), (16 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_EnableVREFBUF), (48 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_HighImpedanceConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_TrimmingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SYSCFG_VREFBUF_VoltageScalingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetFMCMemorySwappingConfig), (20 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32h7xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32h7xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DeInit), (52 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (54 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (40 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (296 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (292 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (300 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (132 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (324 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (160 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (400 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (224 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (340 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (172 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (356 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (168 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (344 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (176 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (312 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (232 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (96 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (368 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (364 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (208 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (384 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (280 bytes).
    Removing stm32h7xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (128 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (26 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAError), (332 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterReceiveCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMAMasterTransmitCplt), (82 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveReceiveCplt), (164 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_DMASlaveTransmitCplt), (30 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Enable_IRQ), (136 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterCplt), (244 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_ITMasterSeqCplt), (80 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_DMA), (272 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Master_ISR_IT), (304 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_DMA), (316 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Mem_ISR_IT), (328 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryRead), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (100 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_DMA), (524 bytes).
    Removing stm32h7xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (168 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableFastModePlus), (40 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_DisableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableWakeUp), (80 bytes).
    Removing stm32h7xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (200 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (28 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (236 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_GetPending), (32 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (48 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (18 bytes).
    Removing stm32h7xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (268 bytes).
    Removing stm32h7xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start), (148 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (220 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (160 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (276 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (108 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (108 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (432 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (184 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (112 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (182 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (154 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (40 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_GetChannelState), (54 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (290 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Init), (98 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start), (304 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (540 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop), (118 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (198 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (182 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_IRQHandler), (328 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (110 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Init), (98 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start), (292 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (548 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop), (176 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (256 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (240 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (248 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (94 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (144 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (148 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (168 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (100 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start), (292 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (548 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (344 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (176 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (256 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (240 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (44 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (224 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_TI1_SetConfig), (148 bytes).
    Removing stm32h7xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_Break2Callback), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (160 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakInput), (204 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (128 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (160 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (128 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_DisarmBreakInput), (72 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_GroupChannel5), (58 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (220 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (200 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (252 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (208 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (64 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (72 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (74 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (224 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (424 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (268 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (172 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (102 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (124 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (224 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (424 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (268 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (172 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_ReArmBreakInput), (116 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_TISelection), (96 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32h7xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32h7xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_HalfDuplex_Init), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_Init), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_LIN_SendBreak), (50 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_DisableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnableMuteMode), (52 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (12 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_MultiProcessor_Init), (138 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort), (256 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive), (172 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (188 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit), (140 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Abort_IT), (292 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAPause), (116 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAResume), (108 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DMAStop), (146 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DeInit), (70 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_DisableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_EnableReceiverTimeout), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_GetState), (14 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive), (232 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_DMA), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Receive_IT), (72 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_ReceiverTimeout_Config), (24 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit), (176 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (144 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_Transmit_IT), (152 bytes).
    Removing stm32h7xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAError), (78 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMAReceiveCplt), (130 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxAbortCallback), (68 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxHalfCplt), (32 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATransmitCplt), (64 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxAbortCallback), (76 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (40 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_EndTxTransfer), (46 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_16BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT), (200 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_RxISR_8BIT_FIFOEN), (416 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_DMA), (168 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_Start_Receive_IT), (272 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT), (82 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_16BIT_FIFOEN), (106 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT), (78 bytes).
    Removing stm32h7xx_hal_uart.o(i.UART_TxISR_8BIT_FIFOEN), (102 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rev16_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.revsh_text), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(.rrx_text), (6 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_MultiProcessorEx_AddressLength_Set), (48 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_RS485Ex_Init), (140 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableFifoMode), (72 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_EnableStopMode), (46 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle), (304 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_StopModeWakeUpSourceConfig), (140 bytes).
    Removing system_stm32h7xx.o(.rev16_text), (4 bytes).
    Removing system_stm32h7xx.o(.revsh_text), (4 bytes).
    Removing system_stm32h7xx.o(.rrx_text), (6 bytes).
    Removing system_stm32h7xx.o(i.SystemCoreClockUpdate), (320 bytes).
    Removing esp8266.o(.rev16_text), (4 bytes).
    Removing esp8266.o(.revsh_text), (4 bytes).
    Removing esp8266.o(.rrx_text), (6 bytes).
    Removing esp8266.o(i.ESP8266_ConnectAliyun), (532 bytes).
    Removing esp8266.o(i.ESP8266_ConnectWiFi), (112 bytes).
    Removing esp8266.o(i.ESP8266_Init), (136 bytes).
    Removing esp8266.o(i.ESP8266_PublishData), (124 bytes).
    Removing esp8266.o(i.ESP8266_SendCommand), (136 bytes).
    Removing esp8266.o(i.HAL_UART_RxCpltCallback), (48 bytes).
    Removing esp8266.o(.bss), (512 bytes).
    Removing esp8266.o(.conststring), (65 bytes).
    Removing esp8266.o(.data), (4 bytes).
    Removing gps.o(.rev16_text), (4 bytes).
    Removing gps.o(.revsh_text), (4 bytes).
    Removing gps.o(.rrx_text), (6 bytes).
    Removing gps.o(i.GPS_DMtoD), (48 bytes).
    Removing gps.o(i.GPS_Init), (24 bytes).
    Removing gps.o(i.GPS_Process), (552 bytes).
    Removing gps.o(i.HAL_UART2_RxCpltCallback), (44 bytes).
    Removing gps.o(.bss), (384 bytes).
    Removing gps.o(.data), (6 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing motor.o(i.Encoder_GetCount), (12 bytes).
    Removing motor.o(i.Encoder_GetDistanceMM), (28 bytes).
    Removing motor.o(i.Encoder_Init), (32 bytes).
    Removing motor.o(i.Encoder_Reset), (20 bytes).
    Removing motor.o(i.Encoder_Update), (56 bytes).
    Removing motor.o(i.Motor_Control), (60 bytes).
    Removing motor.o(i.Motor_Init), (2 bytes).
    Removing motor.o(.data), (12 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_DrawArc), (582 bytes).
    Removing oled.o(i.OLED_DrawCircle), (356 bytes).
    Removing oled.o(i.OLED_DrawEllipse), (658 bytes).
    Removing oled.o(i.OLED_DrawLine), (236 bytes).
    Removing oled.o(i.OLED_DrawPoint), (40 bytes).
    Removing oled.o(i.OLED_DrawRectangle), (122 bytes).
    Removing oled.o(i.OLED_DrawTriangle), (198 bytes).
    Removing oled.o(i.OLED_GetPoint), (44 bytes).
    Removing oled.o(i.OLED_I2C_SendByte), (2 bytes).
    Removing oled.o(i.OLED_I2C_Start), (2 bytes).
    Removing oled.o(i.OLED_I2C_Stop), (2 bytes).
    Removing oled.o(i.OLED_IsInAngle), (96 bytes).
    Removing oled.o(i.OLED_Pow), (14 bytes).
    Removing oled.o(i.OLED_Printf), (42 bytes).
    Removing oled.o(i.OLED_Reverse), (40 bytes).
    Removing oled.o(i.OLED_ReverseArea), (104 bytes).
    Removing oled.o(i.OLED_ShowBinNum), (64 bytes).
    Removing oled.o(i.OLED_ShowChinese), (160 bytes).
    Removing oled.o(i.OLED_ShowFloatNum), (164 bytes).
    Removing oled.o(i.OLED_ShowHexNum), (72 bytes).
    Removing oled.o(i.OLED_ShowNum), (70 bytes).
    Removing oled.o(i.OLED_ShowSignedNum), (94 bytes).
    Removing oled.o(i.OLED_UpdateArea), (100 bytes).
    Removing oled.o(i.OLED_pnpoly), (116 bytes).
    Removing oled_font.o(.constdata), (665 bytes).
    Removing oled_font.o(.constdata), (32 bytes).
    Removing servo.o(.rev16_text), (4 bytes).
    Removing servo.o(.revsh_text), (4 bytes).
    Removing servo.o(.rrx_text), (6 bytes).
    Removing servo.o(i.SERVO_Clamp), (6 bytes).
    Removing servo.o(i.SERVO_Init), (28 bytes).
    Removing servo.o(i.SERVO_Release), (8 bytes).
    Removing servo.o(i.SERVO_SetPulse), (32 bytes).
    Removing servo.o(i.SERVO_SetSpeed), (56 bytes).
    Removing servo.o(i.SERVO_Stop), (8 bytes).
    Removing servo.o(.data), (4 bytes).
    Removing v831.o(.rev16_text), (4 bytes).
    Removing v831.o(.revsh_text), (4 bytes).
    Removing v831.o(.rrx_text), (6 bytes).
    Removing v831.o(i.HAL_UART6_RxCpltCallback), (48 bytes).
    Removing v831.o(i.ProcessByte), (168 bytes).
    Removing v831.o(i.V831_Init), (40 bytes).
    Removing v831.o(i.V831_ProcessData), (76 bytes).
    Removing v831.o(i.V831_RequestDetection), (60 bytes).
    Removing v831.o(i.V831_SendHeartbeat), (60 bytes).
    Removing v831.o(.bss), (150 bytes).
    Removing v831.o(.data), (10 bytes).
    Removing vl53l0x.o(.rev16_text), (4 bytes).
    Removing vl53l0x.o(.revsh_text), (4 bytes).
    Removing vl53l0x.o(.rrx_text), (6 bytes).
    Removing vl53l0x.o(i.print_pal_error), (52 bytes).
    Removing vl53l0x.o(i.vl53l0x_Addr_set), (136 bytes).
    Removing vl53l0x.o(i.vl53l0x_info), (256 bytes).
    Removing vl53l0x.o(i.vl53l0x_init), (164 bytes).
    Removing vl53l0x.o(i.vl53l0x_reset), (72 bytes).
    Removing vl53l0x.o(i.vl53l0x_test), (36 bytes).
    Removing vl53l0x.o(.bss), (475 bytes).
    Removing vl53l0x.o(.data), (1 bytes).
    Removing vl53l0x.o(.data), (56 bytes).
    Removing vl53l0x_api.o(.rev16_text), (4 bytes).
    Removing vl53l0x_api.o(.revsh_text), (4 bytes).
    Removing vl53l0x_api.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_CheckAndLoadInterruptSettings), (124 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_ClearInterruptMask), (74 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_DataInit), (408 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_EnableInterruptMask), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceErrorStatus), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceErrorString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceMode), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDeviceParameters), (128 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetDmaxCalParameters), (18 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetFractionEnable), (26 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetGpioConfig), (118 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetHistogramMeasurementData), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetHistogramMode), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetInterMeasurementPeriodMilliSeconds), (54 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetInterruptMaskStatus), (34 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetInterruptThresholds), (58 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckCurrent), (72 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckEnable), (26 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckStatus), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLimitCheckValue), (126 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetLinearityCorrectiveGain), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMaxNumberOfROIZones), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMeasurementDataReady), (58 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMeasurementRefSignal), (42 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetMeasurementTimingBudgetMicroSeconds), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfLimitCheck), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfROIZones), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetNumberOfSequenceSteps), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetOffsetCalibrationDataMicroMeter), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalErrorString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalSpecVersion), (26 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalState), (10 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPalStateString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetPowerMode), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetProductRevision), (28 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetRangeStatusString), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetRangingMeasurementData), (320 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetRefCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetReferenceSpads), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnable), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepEnables), (114 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepTimeout), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSequenceStepsInfo), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperFactor), (48 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetSpadAmbientDamperThreshold), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetStopCompletedStatus), (130 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetTotalSignalRate), (34 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetTuningSettingBuffer), (18 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetUpperLimitMilliMeter), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetVcselPulsePeriod), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetVersion), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetWrapAroundCheckEnable), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationEnable), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_GetXTalkCompensationRateMegaCps), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformOffsetCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformRefCalibration), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformRefSpadManagement), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformSingleHistogramMeasurement), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformSingleMeasurement), (34 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformSingleRangingMeasurement), (52 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformXTalkCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_PerformXTalkMeasurement), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_ResetDevice), (90 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDeviceAddress), (8 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDeviceMode), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDeviceParameters), (116 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetDmaxCalParameters), (48 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetGpioConfig), (312 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetGroupParamHold), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetHistogramMode), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetInterMeasurementPeriodMilliSeconds), (48 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetInterruptThresholds), (38 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetLimitCheckEnable), (134 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetLimitCheckValue), (80 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetLinearityCorrectiveGain), (36 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetMeasurementTimingBudgetMicroSeconds), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetNumberOfROIZones), (12 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetOffsetCalibrationDataMicroMeter), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetPowerMode), (72 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetRangeFractionEnable), (24 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetRefCalibration), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetReferenceSpads), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSequenceStepEnable), (176 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSequenceStepTimeout), (90 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperFactor), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetSpadAmbientDamperThreshold), (44 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetTuningSettingBuffer), (40 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetVcselPulsePeriod), (4 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetWrapAroundCheckEnable), (68 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationEnable), (52 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_SetXTalkCompensationRateMegaCps), (46 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_StartMeasurement), (212 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_StaticInit), (372 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_StopMeasurement), (86 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_WaitDeviceBooted), (6 bytes).
    Removing vl53l0x_api.o(i.VL53L0X_WaitDeviceReadyForNewMeasurement), (6 bytes).
    Removing vl53l0x_api.o(i.sequence_step_enabled), (56 bytes).
    Removing vl53l0x_api.o(.data), (681 bytes).
    Removing vl53l0x_api_calibration.o(.rev16_text), (4 bytes).
    Removing vl53l0x_api_calibration.o(.revsh_text), (4 bytes).
    Removing vl53l0x_api_calibration.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_apply_offset_adjustment), (50 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_get_offset_calibration_data_micro_meter), (52 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_get_ref_calibration), (26 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_get_reference_spads), (114 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_offset_calibration), (198 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_phase_calibration), (116 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_calibration), (64 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_ref_spad_management), (540 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_single_ref_calibration), (56 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_vhv_calibration), (116 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_perform_xtalk_calibration), (216 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_ref_calibration_io), (178 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_set_offset_calibration_data_micro_meter), (64 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_set_ref_calibration), (32 bytes).
    Removing vl53l0x_api_calibration.o(i.VL53L0X_set_reference_spads), (166 bytes).
    Removing vl53l0x_api_calibration.o(i.count_enabled_spads), (148 bytes).
    Removing vl53l0x_api_calibration.o(i.enable_ref_spads), (148 bytes).
    Removing vl53l0x_api_calibration.o(i.enable_spad_bit), (42 bytes).
    Removing vl53l0x_api_calibration.o(i.get_next_good_spad), (96 bytes).
    Removing vl53l0x_api_calibration.o(i.get_ref_spad_map), (10 bytes).
    Removing vl53l0x_api_calibration.o(i.is_aperture), (24 bytes).
    Removing vl53l0x_api_calibration.o(i.perform_ref_signal_measurement), (100 bytes).
    Removing vl53l0x_api_calibration.o(i.set_ref_spad_map), (10 bytes).
    Removing vl53l0x_api_calibration.o(.data), (16 bytes).
    Removing vl53l0x_api_core.o(.rev16_text), (4 bytes).
    Removing vl53l0x_api_core.o(.revsh_text), (4 bytes).
    Removing vl53l0x_api_core.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_calc_dmax), (224 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_calc_macro_period_ps), (16 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_calc_sigma_estimate), (568 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_calc_timeout_mclks), (34 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_calc_timeout_us), (34 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_decode_timeout), (12 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_decode_vcsel_period), (8 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_device_read_strobe), (74 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_encode_timeout), (32 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_encode_vcsel_period), (10 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_info_from_device), (1322 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_measurement_timing_budget_micro_seconds), (198 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_pal_range_status), (482 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_total_signal_rate), (28 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_total_xtalk_rate), (46 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_get_vcsel_pulse_period), (48 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_isqrt), (42 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_load_tuning_settings), (164 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_measurement_poll_for_completion), (54 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_quadrature_sum), (28 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_reverse_bytes), (32 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_set_measurement_timing_budget_micro_seconds), (196 bytes).
    Removing vl53l0x_api_core.o(i.VL53L0X_set_vcsel_pulse_period), (546 bytes).
    Removing vl53l0x_api_core.o(i.get_sequence_step_timeout), (244 bytes).
    Removing vl53l0x_api_core.o(i.set_sequence_step_timeout), (260 bytes).
    Removing vl53l0x_api_ranging.o(.rev16_text), (4 bytes).
    Removing vl53l0x_api_ranging.o(.revsh_text), (4 bytes).
    Removing vl53l0x_api_ranging.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api_strings.o(.rev16_text), (4 bytes).
    Removing vl53l0x_api_strings.o(.revsh_text), (4 bytes).
    Removing vl53l0x_api_strings.o(.rrx_text), (6 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_check_part_used), (60 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_device_error_string), (464 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_device_info), (180 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_limit_check_info), (204 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_pal_error_string), (596 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_pal_state_string), (192 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_range_status_string), (168 bytes).
    Removing vl53l0x_api_strings.o(i.VL53L0X_get_sequence_steps_info), (96 bytes).
    Removing vl53l0x_gen.o(.rev16_text), (4 bytes).
    Removing vl53l0x_gen.o(.revsh_text), (4 bytes).
    Removing vl53l0x_gen.o(.rrx_text), (6 bytes).
    Removing vl53l0x_gen.o(i.delay_ms), (4 bytes).
    Removing vl53l0x_gen.o(i.vl53l0x_general_start), (64 bytes).
    Removing vl53l0x_gen.o(i.vl53l0x_general_test), (4 bytes).
    Removing vl53l0x_gen.o(i.vl53l0x_set_mode), (324 bytes).
    Removing vl53l0x_gen.o(i.vl53l0x_start_single_test), (52 bytes).
    Removing vl53l0x_gen.o(.bss), (60 bytes).
    Removing vl53l0x_gen.o(.data), (2 bytes).
    Removing vl53l0x_i2c.o(.rev16_text), (4 bytes).
    Removing vl53l0x_i2c.o(.revsh_text), (4 bytes).
    Removing vl53l0x_i2c.o(.rrx_text), (6 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_i2c_init), (76 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_read_byte), (8 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_read_dword), (42 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_read_multi), (14 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_read_word), (26 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_write_byte), (12 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_write_dword), (34 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_write_multi), (14 bytes).
    Removing vl53l0x_i2c.o(i.VL53L0X_write_word), (22 bytes).
    Removing vl53l0x_i2c.o(i.VL_IIC_Read_nByte), (36 bytes).
    Removing vl53l0x_i2c.o(i.VL_IIC_Write_nByte), (36 bytes).
    Removing vl53l0x_platform.o(.rev16_text), (4 bytes).
    Removing vl53l0x_platform.o(.revsh_text), (4 bytes).
    Removing vl53l0x_platform.o(.rrx_text), (6 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_PollingDelay), (18 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_RdByte), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_RdDWord), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_RdWord), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_ReadMulti), (32 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_UpdateByte), (66 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_WrByte), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_WrDWord), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_WrWord), (22 bytes).
    Removing vl53l0x_platform.o(i.VL53L0X_WriteMulti), (32 bytes).

907 unused section(s) (total 80991 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ../Core/Src/system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_io.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_stdio.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memmove_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m_pel.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file_locked.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  flsbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fseek.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  stdio_streams.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fwritefast.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ftell.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputs.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fflush.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fopen.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fputc_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  initio_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  fclose.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  setvbuf_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  streamlock.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strstr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcpy.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/wchar.c                          0x00000000   Number         0  flsbuf_fwide.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32h7xx_hal_msp.c          0x00000000   Number         0  stm32h7xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32h7xx_it.c               0x00000000   Number         0  stm32h7xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32h7xx.c           0x00000000   Number         0  system_stm32h7xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c 0x00000000   Number         0  stm32h7xx_hal.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c 0x00000000   Number         0  stm32h7xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c 0x00000000   Number         0  stm32h7xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c 0x00000000   Number         0  stm32h7xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c 0x00000000   Number         0  stm32h7xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c 0x00000000   Number         0  stm32h7xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c 0x00000000   Number         0  stm32h7xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c 0x00000000   Number         0  stm32h7xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c 0x00000000   Number         0  stm32h7xx_hal_hsem.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c 0x00000000   Number         0  stm32h7xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c 0x00000000   Number         0  stm32h7xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c 0x00000000   Number         0  stm32h7xx_hal_mdma.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c 0x00000000   Number         0  stm32h7xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c 0x00000000   Number         0  stm32h7xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c 0x00000000   Number         0  stm32h7xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c 0x00000000   Number         0  stm32h7xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim.c 0x00000000   Number         0  stm32h7xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim_ex.c 0x00000000   Number         0  stm32h7xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c 0x00000000   Number         0  stm32h7xx_hal_uart.o ABSOLUTE
    ..\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c 0x00000000   Number         0  stm32h7xx_hal_uart_ex.o ABSOLUTE
    ..\HARDWARE\Src\OLED.c                   0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\Src\OLED_Font.c              0x00000000   Number         0  oled_font.o ABSOLUTE
    ..\HARDWARE\Src\Servo.c                  0x00000000   Number         0  servo.o ABSOLUTE
    ..\HARDWARE\Src\SysTick.c                0x00000000   Number         0  systick.o ABSOLUTE
    ..\HARDWARE\Src\esp8266.c                0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\HARDWARE\Src\gps.c                    0x00000000   Number         0  gps.o ABSOLUTE
    ..\HARDWARE\Src\motor.c                  0x00000000   Number         0  motor.o ABSOLUTE
    ..\HARDWARE\Src\v831.c                   0x00000000   Number         0  v831.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x.c        0x00000000   Number         0  vl53l0x.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_api.c    0x00000000   Number         0  vl53l0x_api.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_api_calibration.c 0x00000000   Number         0  vl53l0x_api_calibration.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_api_core.c 0x00000000   Number         0  vl53l0x_api_core.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_api_ranging.c 0x00000000   Number         0  vl53l0x_api_ranging.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_api_strings.c 0x00000000   Number         0  vl53l0x_api_strings.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_gen.c    0x00000000   Number         0  vl53l0x_gen.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_i2c.c    0x00000000   Number         0  vl53l0x_i2c.o ABSOLUTE
    ..\HARDWARE\VL53L0X\src\vl53l0x_platform.c 0x00000000   Number         0  vl53l0x_platform.o ABSOLUTE
    ..\\HARDWARE\\Src\\OLED.c                0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\Src\\Servo.c               0x00000000   Number         0  servo.o ABSOLUTE
    ..\\HARDWARE\\Src\\esp8266.c             0x00000000   Number         0  esp8266.o ABSOLUTE
    ..\\HARDWARE\\Src\\gps.c                 0x00000000   Number         0  gps.o ABSOLUTE
    ..\\HARDWARE\\Src\\motor.c               0x00000000   Number         0  motor.o ABSOLUTE
    ..\\HARDWARE\\Src\\v831.c                0x00000000   Number         0  v831.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x.c    0x00000000   Number         0  vl53l0x.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_api.c 0x00000000   Number         0  vl53l0x_api.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_api_calibration.c 0x00000000   Number         0  vl53l0x_api_calibration.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_api_core.c 0x00000000   Number         0  vl53l0x_api_core.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_api_ranging.c 0x00000000   Number         0  vl53l0x_api_ranging.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_api_strings.c 0x00000000   Number         0  vl53l0x_api_strings.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_gen.c 0x00000000   Number         0  vl53l0x_gen.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_i2c.c 0x00000000   Number         0  vl53l0x_i2c.o ABSOLUTE
    ..\\HARDWARE\\VL53L0X\\src\\vl53l0x_platform.c 0x00000000   Number         0  vl53l0x_platform.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32h723xx.s                    0x00000000   Number         0  startup_stm32h723xx.o ABSOLUTE
    RESET                                    0x08000000   Section      716  startup_stm32h723xx.o(RESET)
    !!!main                                  0x080002cc   Section        8  __main.o(!!!main)
    !!!scatter                               0x080002d4   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000308   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000324   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$libinit$$00000000          0x08000340   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x08000342   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000346   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x08000346   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000011          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x0800034e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x0800034e   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000350   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000352   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000352   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000354   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000354   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000354   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800035a   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800035a   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x0800035e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x0800035e   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x08000366   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000368   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000368   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x0800036c   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x08000374   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x08000374   Section       72  startup_stm32h723xx.o(.text)
    $v0                                      0x08000374   Number         0  startup_stm32h723xx.o(.text)
    .text                                    0x080003bc   Section        0  h1_alloc.o(.text)
    .text                                    0x0800041a   Section        0  h1_free.o(.text)
    .text                                    0x08000468   Section      238  lludivv7m.o(.text)
    .text                                    0x08000556   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080005a4   Section        0  heapauxi.o(.text)
    .text                                    0x080005ac   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x080005b4   Section        0  hguard.o(.text)
    .text                                    0x080005b8   Section        0  init_alloc.o(.text)
    .text                                    0x08000642   Section        0  h1_init.o(.text)
    .text                                    0x08000650   Section        8  libspace.o(.text)
    .text                                    0x08000658   Section        0  h1_extend.o(.text)
    .text                                    0x0800068c   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x0800069a   Section        2  use_no_semi.o(.text)
    .text                                    0x0800069c   Section        0  indicate_semi.o(.text)
    .text                                    0x0800069c   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x080006e6   Section        0  exit.o(.text)
    .text                                    0x080006f8   Section        0  defsig_exit.o(.text)
    .text                                    0x08000704   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08000754   Section        0  sys_exit.o(.text)
    .text                                    0x08000760   Section        0  defsig_general.o(.text)
    .text                                    0x08000792   Section        0  sys_wrch.o(.text)
    i.BusFault_Handler                       0x080007a0   Section        0  stm32h7xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x080007a4   Section        0  stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080007b0   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080007b1   Thumb Code   168  stm32h7xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CalcDMAMUXChannelBaseAndMask       0x08000864   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    DMA_CalcDMAMUXChannelBaseAndMask         0x08000865   Thumb Code   138  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXChannelBaseAndMask)
    i.DMA_CalcDMAMUXRequestGenBaseAndMask    0x08000900   Section        0  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    DMA_CalcDMAMUXRequestGenBaseAndMask      0x08000901   Thumb Code   104  stm32h7xx_hal_dma.o(i.DMA_CalcDMAMUXRequestGenBaseAndMask)
    i.DMA_CheckFifoParam                     0x08000974   Section        0  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08000975   Thumb Code    84  stm32h7xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DebugMon_Handler                       0x080009c8   Section        0  stm32h7xx_it.o(i.DebugMon_Handler)
    i.Error_Handler                          0x080009ca   Section        0  main.o(i.Error_Handler)
    i.ExitRun0Mode                           0x080009cc   Section        0  system_stm32h7xx.o(i.ExitRun0Mode)
    i.HAL_DMA_Abort                          0x080009e8   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08000d48   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_GetState                       0x08000fcc   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_GetState)
    i.HAL_DMA_IRQHandler                     0x08000fd4   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080016bc   Section        0  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_GPIO_Init                          0x08001a78   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08001c94   Section        0  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08001ca0   Section        0  stm32h7xx_hal.o(i.HAL_GetTick)
    i.HAL_I2CEx_ConfigAnalogFilter           0x08001cac   Section        0  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    i.HAL_I2CEx_ConfigDigitalFilter          0x08001d04   Section        0  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    i.HAL_I2CEx_EnableFastModePlus           0x08001d58   Section        0  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus)
    i.HAL_I2C_AbortCpltCallback              0x08001d80   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    i.HAL_I2C_AddrCallback                   0x08001d82   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    i.HAL_I2C_ER_IRQHandler                  0x08001d84   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    i.HAL_I2C_EV_IRQHandler                  0x08001dec   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    i.HAL_I2C_ErrorCallback                  0x08001dfc   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    i.HAL_I2C_Init                           0x08001e00   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_ListenCpltCallback             0x08001ec0   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    i.HAL_I2C_Master_Transmit                0x08001ec4   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_MspInit                        0x08002010   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_I2C_SlaveRxCpltCallback            0x08002190   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    i.HAL_I2C_SlaveTxCpltCallback            0x08002192   Section        0  stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    i.HAL_IncTick                            0x08002194   Section        0  stm32h7xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x080021a4   Section        0  stm32h7xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002200   Section        0  stm32h7xx_hal.o(i.HAL_InitTick)
    i.HAL_MPU_ConfigRegion                   0x08002240   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    i.HAL_MPU_Disable                        0x0800229c   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    i.HAL_MPU_Enable                         0x080022b8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    i.HAL_MspInit                            0x080022dc   Section        0  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x080022f8   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002314   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002354   Section        0  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_PWREx_ConfigSupply                 0x08002378   Section        0  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    i.HAL_RCCEx_GetD3PCLK1Freq               0x080023cc   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    i.HAL_RCCEx_GetPLL2ClockFreq             0x080023f0   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    i.HAL_RCCEx_GetPLL3ClockFreq             0x08002534   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    i.HAL_RCCEx_PeriphCLKConfig              0x08002678   Section        0  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    i.HAL_RCC_ClockConfig                    0x08002fb4   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08003210   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08003254   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003278   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x0800329c   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080033d4   Section        0  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSCFG_AnalogSwitchConfig          0x080038fc   Section        0  stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig)
    i.HAL_SYSTICK_Config                     0x0800390c   Section        0  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003934   Section        0  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080039ec   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08003a50   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08003a78   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08003b74   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08003c30   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_MspPostInit                    0x08003c98   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08003cec   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08003e10   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08003e72   Section        0  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_UARTEx_DisableFifoMode             0x08003e74   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    i.HAL_UARTEx_RxEventCallback             0x08003eb4   Section        0  stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UARTEx_RxFifoFullCallback          0x08003eb6   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    i.HAL_UARTEx_SetRxFifoThreshold          0x08003eb8   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    i.HAL_UARTEx_SetTxFifoThreshold          0x08003f04   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    i.HAL_UARTEx_TxFifoEmptyCallback         0x08003f50   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    i.HAL_UARTEx_WakeupCallback              0x08003f52   Section        0  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    i.HAL_UART_ErrorCallback                 0x08003f54   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003f58   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080042f4   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004360   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_TxCpltCallback                0x080044dc   Section        0  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x080044de   Section        0  stm32h7xx_it.o(i.HardFault_Handler)
    i.I2C3_ER_IRQHandler                     0x080044e0   Section        0  stm32h7xx_it.o(i.I2C3_ER_IRQHandler)
    i.I2C3_EV_IRQHandler                     0x080044ec   Section        0  stm32h7xx_it.o(i.I2C3_EV_IRQHandler)
    i.I2C_DMAAbort                           0x080044f8   Section        0  stm32h7xx_hal_i2c.o(i.I2C_DMAAbort)
    I2C_DMAAbort                             0x080044f9   Thumb Code    20  stm32h7xx_hal_i2c.o(i.I2C_DMAAbort)
    i.I2C_Disable_IRQ                        0x0800450c   Section        0  stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ)
    I2C_Disable_IRQ                          0x0800450d   Thumb Code    96  stm32h7xx_hal_i2c.o(i.I2C_Disable_IRQ)
    i.I2C_Flush_TXDR                         0x0800456c   Section        0  stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR)
    I2C_Flush_TXDR                           0x0800456d   Thumb Code    34  stm32h7xx_hal_i2c.o(i.I2C_Flush_TXDR)
    i.I2C_ITAddrCplt                         0x0800458e   Section        0  stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt)
    I2C_ITAddrCplt                           0x0800458f   Thumb Code   148  stm32h7xx_hal_i2c.o(i.I2C_ITAddrCplt)
    i.I2C_ITError                            0x08004624   Section        0  stm32h7xx_hal_i2c.o(i.I2C_ITError)
    I2C_ITError                              0x08004625   Thumb Code   274  stm32h7xx_hal_i2c.o(i.I2C_ITError)
    i.I2C_ITListenCplt                       0x08004744   Section        0  stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt)
    I2C_ITListenCplt                         0x08004745   Thumb Code    96  stm32h7xx_hal_i2c.o(i.I2C_ITListenCplt)
    i.I2C_ITSlaveCplt                        0x080047a8   Section        0  stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt)
    I2C_ITSlaveCplt                          0x080047a9   Thumb Code   552  stm32h7xx_hal_i2c.o(i.I2C_ITSlaveCplt)
    i.I2C_ITSlaveSeqCplt                     0x080049f8   Section        0  stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt)
    I2C_ITSlaveSeqCplt                       0x080049f9   Thumb Code   114  stm32h7xx_hal_i2c.o(i.I2C_ITSlaveSeqCplt)
    i.I2C_IsErrorOccurred                    0x08004a6c   Section        0  stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    I2C_IsErrorOccurred                      0x08004a6d   Thumb Code   268  stm32h7xx_hal_i2c.o(i.I2C_IsErrorOccurred)
    i.I2C_Slave_ISR_IT                       0x08004b7c   Section        0  stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT)
    I2C_Slave_ISR_IT                         0x08004b7d   Thumb Code   276  stm32h7xx_hal_i2c.o(i.I2C_Slave_ISR_IT)
    i.I2C_TransferConfig                     0x08004c94   Section        0  stm32h7xx_hal_i2c.o(i.I2C_TransferConfig)
    I2C_TransferConfig                       0x08004c95   Thumb Code    44  stm32h7xx_hal_i2c.o(i.I2C_TransferConfig)
    i.I2C_TreatErrorCallback                 0x08004cc4   Section        0  stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback)
    I2C_TreatErrorCallback                   0x08004cc5   Thumb Code    42  stm32h7xx_hal_i2c.o(i.I2C_TreatErrorCallback)
    i.I2C_WaitOnFlagUntilTimeout             0x08004cee   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08004cef   Thumb Code   124  stm32h7xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnSTOPFlagUntilTimeout         0x08004d6a   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    I2C_WaitOnSTOPFlagUntilTimeout           0x08004d6b   Thumb Code    88  stm32h7xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout)
    i.I2C_WaitOnTXISFlagUntilTimeout         0x08004dc2   Section        0  stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    I2C_WaitOnTXISFlagUntilTimeout           0x08004dc3   Thumb Code    92  stm32h7xx_hal_i2c.o(i.I2C_WaitOnTXISFlagUntilTimeout)
    i.MX_DMA_Init                            0x08004e20   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08004e4c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08004ef0   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C3_Init                           0x08004f48   Section        0  i2c.o(i.MX_I2C3_Init)
    i.MX_TIM1_Init                           0x08004fa4   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM4_Init                           0x08005014   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_USART1_UART_Init                    0x080050b4   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08005120   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART6_UART_Init                    0x08005188   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x080051f0   Section        0  stm32h7xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x080051f2   Section        0  stm32h7xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x080051f4   Section        0  oled.o(i.OLED_Clear)
    i.OLED_ClearArea                         0x0800521c   Section        0  oled.o(i.OLED_ClearArea)
    i.OLED_GPIO_Init                         0x08005288   Section        0  oled.o(i.OLED_GPIO_Init)
    i.OLED_Init                              0x0800529e   Section        0  oled.o(i.OLED_Init)
    i.OLED_SetCursor                         0x0800533a   Section        0  oled.o(i.OLED_SetCursor)
    i.OLED_ShowChar                          0x0800535c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowImage                         0x08005398   Section        0  oled.o(i.OLED_ShowImage)
    i.OLED_ShowString                        0x08005448   Section        0  oled.o(i.OLED_ShowString)
    i.OLED_Update                            0x08005474   Section        0  oled.o(i.OLED_Update)
    i.OLED_WriteCommand                      0x0800549c   Section        0  oled.o(i.OLED_WriteCommand)
    i.OLED_WriteData                         0x080054bc   Section        0  oled.o(i.OLED_WriteData)
    i.PendSV_Handler                         0x08005500   Section        0  stm32h7xx_it.o(i.PendSV_Handler)
    i.RCCEx_PLL2_Config                      0x08005504   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    RCCEx_PLL2_Config                        0x08005505   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL2_Config)
    i.RCCEx_PLL3_Config                      0x08005624   Section        0  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    RCCEx_PLL3_Config                        0x08005625   Thumb Code   284  stm32h7xx_hal_rcc_ex.o(i.RCCEx_PLL3_Config)
    i.SVC_Handler                            0x08005744   Section        0  stm32h7xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08005746   Section        0  stm32h7xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x0800574c   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x080057d4   Section        0  system_stm32h7xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x080058ac   Section        0  stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x0800598c   Section        0  stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080059a0   Section        0  stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080059a1   Thumb Code    16  stm32h7xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080059b4   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080059b5   Thumb Code   140  stm32h7xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08005a58   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08005af0   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08005af1   Thumb Code   126  stm32h7xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08005b88   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08005b89   Thumb Code    96  stm32h7xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_OC5_SetConfig                      0x08005c00   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig)
    TIM_OC5_SetConfig                        0x08005c01   Thumb Code    90  stm32h7xx_hal_tim.o(i.TIM_OC5_SetConfig)
    i.TIM_OC6_SetConfig                      0x08005c74   Section        0  stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig)
    TIM_OC6_SetConfig                        0x08005c75   Thumb Code    90  stm32h7xx_hal_tim.o(i.TIM_OC6_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005ce8   Section        0  stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005ce9   Thumb Code    34  stm32h7xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08005d0a   Section        0  stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08005d0b   Thumb Code    36  stm32h7xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UARTEx_SetNbDataToProcess              0x08005d30   Section        0  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    UARTEx_SetNbDataToProcess                0x08005d31   Thumb Code    62  stm32h7xx_hal_uart_ex.o(i.UARTEx_SetNbDataToProcess)
    i.UART_AdvFeatureConfig                  0x08005d74   Section        0  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    i.UART_CheckIdleState                    0x08005e3c   Section        0  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    i.UART_DMAAbortOnError                   0x08005ee6   Section        0  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08005ee7   Thumb Code    16  stm32h7xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x08005ef8   Section        0  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08005ef9   Thumb Code    78  stm32h7xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_SetConfig                         0x08005f4c   Section        0  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    i.UART_WaitOnFlagUntilTimeout            0x08006328   Section        0  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080063bc   Section        0  stm32h7xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x080063c8   Section        0  stm32h7xx_it.o(i.USART2_IRQHandler)
    i.UsageFault_Handler                     0x080063d4   Section        0  stm32h7xx_it.o(i.UsageFault_Handler)
    i.__NVIC_SetPriority                     0x080063d6   Section        0  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080063d7   Thumb Code    34  stm32h7xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.main                                   0x080063f8   Section        0  main.o(i.main)
    x$fpl$fpinit                             0x080064a0   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080064a0   Number         0  fpinit.o(x$fpl$fpinit)
    .constdata                               0x080064aa   Section        8  stm32h7xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080064aa   Data           8  stm32h7xx_hal_dma.o(.constdata)
    .constdata                               0x080064b2   Section       24  stm32h7xx_hal_uart.o(.constdata)
    .constdata                               0x080064ca   Section       16  stm32h7xx_hal_uart_ex.o(.constdata)
    numerator                                0x080064ca   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    denominator                              0x080064d2   Data           8  stm32h7xx_hal_uart_ex.o(.constdata)
    .constdata                               0x080064da   Section       16  system_stm32h7xx.o(.constdata)
    .constdata                               0x080064ea   Section     1520  oled_font.o(.constdata)
    .constdata                               0x08006ada   Section      570  oled_font.o(.constdata)
    .data                                    0x24000000   Section       12  stm32h7xx_hal.o(.data)
    .data                                    0x2400000c   Section        8  system_stm32h7xx.o(.data)
    .bss                                     0x24000014   Section      288  i2c.o(.bss)
    .bss                                     0x24000134   Section      152  tim.o(.bss)
    .bss                                     0x240001cc   Section      444  usart.o(.bss)
    .bss                                     0x24000388   Section     1024  oled.o(.bss)
    .bss                                     0x24000788   Section       96  libspace.o(.bss)
    HEAP                                     0x240007e8   Section      512  startup_stm32h723xx.o(HEAP)
    Heap_Mem                                 0x240007e8   Data         512  startup_stm32h723xx.o(HEAP)
    STACK                                    0x240009e8   Section     1024  startup_stm32h723xx.o(STACK)
    Stack_Mem                                0x240009e8   Data        1024  startup_stm32h723xx.o(STACK)
    __initial_sp                             0x24000de8   Data           0  startup_stm32h723xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPv5_D16$PE$PLD8$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    __Vectors_Size                           0x000002cc   Number         0  startup_stm32h723xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32h723xx.o(RESET)
    __Vectors_End                            0x080002cc   Data           0  startup_stm32h723xx.o(RESET)
    __main                                   0x080002cd   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080002d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080002d5   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080002d5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x080002e3   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000309   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000325   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    __rt_lib_init                            0x08000341   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000343   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x08000347   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x08000347   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_alloca_1                   0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_heap_1                     0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_collate_1               0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_1                 0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_1               0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_rand_1                     0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_return                     0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_user_alloc_1               0x0800034f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_shutdown                        0x08000351   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000353   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000355   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000355   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000355   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800035b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800035b   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x0800035f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x0800035f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x08000367   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000369   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000369   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x0800036d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x08000375   Thumb Code    12  startup_stm32h723xx.o(.text)
    _maybe_terminate_alloc                   0x08000375   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC3_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    ADC_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel0_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel1_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel2_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel3_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel4_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel5_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel6_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    BDMA_Channel7_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    CEC_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    COMP1_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    CORDIC_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    CRS_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DCMI_PSSI_IRQHandler                     0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT0_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT1_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT2_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DFSDM1_FLT3_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2D_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX1_OVR_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DMAMUX2_OVR_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    DTS_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    ECC_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI0_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI1_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI2_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI3_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI4_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT0_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN1_IT1_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT0_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN2_IT1_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT0_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN3_IT1_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FDCAN_CAL_IRQHandler                     0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FLASH_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMAC_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FMC_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    FPU_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    HSEM1_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_ER_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C4_EV_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_ER_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    I2C5_EV_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM1_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM2_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM3_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM4_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPTIM5_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LPUART1_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_ER_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    LTDC_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDIOS_WKUP_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    MDMA_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI1_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OCTOSPI2_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    PVD_AVD_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    RCC_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    RNG_IRQHandler                           0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI1_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SAI4_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC1_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SDMMC2_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPDIF_RX_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI1_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI2_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI3_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI4_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI5_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SPI6_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    SWPMI1_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM15_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM16_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM17_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_BRK_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM1_UP_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM23_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM24_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM2_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM3_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM4_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM5_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM7_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART4_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART5_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART7_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART8_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    UART9_IRQHandler                         0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART10_IRQHandler                       0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART3_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    USART6_IRQHandler                        0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    WAKEUP_PIN_IRQHandler                    0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    WWDG_IRQHandler                          0x08000393   Thumb Code     0  startup_stm32h723xx.o(.text)
    __user_initial_stackheap                 0x08000395   Thumb Code     0  startup_stm32h723xx.o(.text)
    malloc                                   0x080003bd   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x0800041b   Thumb Code    78  h1_free.o(.text)
    __aeabi_uldivmod                         0x08000469   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000469   Thumb Code   238  lludivv7m.o(.text)
    __aeabi_memclr4                          0x08000557   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000557   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000557   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800055b   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x080005a5   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x080005a7   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x080005a9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_descriptor                     0x080005ad   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x080005b5   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x080005b7   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x080005b9   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x080005bb   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x080005bd   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x080005df   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x080005e5   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x08000643   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x0800064d   Thumb Code     4  h1_init.o(.text)
    __user_libspace                          0x08000651   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000651   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000651   Thumb Code     0  libspace.o(.text)
    __Heap_ProvideMemory                     0x08000659   Thumb Code    52  h1_extend.o(.text)
    __rt_SIGRTMEM                            0x0800068d   Thumb Code    14  defsig_rtmem_outer.o(.text)
    __I$use$semihosting                      0x0800069b   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x0800069b   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x0800069d   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x0800069d   Thumb Code    74  sys_stackheap_outer.o(.text)
    exit                                     0x080006e7   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x080006f9   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTMEM_inner                      0x08000705   Thumb Code    22  defsig_rtmem_inner.o(.text)
    _sys_exit                                0x08000755   Thumb Code     8  sys_exit.o(.text)
    __default_signal_display                 0x08000761   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x08000793   Thumb Code    14  sys_wrch.o(.text)
    BusFault_Handler                         0x080007a1   Thumb Code     2  stm32h7xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x080007a5   Thumb Code     6  stm32h7xx_it.o(i.DMA1_Stream0_IRQHandler)
    DebugMon_Handler                         0x080009c9   Thumb Code     2  stm32h7xx_it.o(i.DebugMon_Handler)
    Error_Handler                            0x080009cb   Thumb Code     2  main.o(i.Error_Handler)
    ExitRun0Mode                             0x080009cd   Thumb Code    22  system_stm32h7xx.o(i.ExitRun0Mode)
    HAL_DMA_Abort                            0x080009e9   Thumb Code   836  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08000d49   Thumb Code   602  stm32h7xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_GetState                         0x08000fcd   Thumb Code     6  stm32h7xx_hal_dma.o(i.HAL_DMA_GetState)
    HAL_DMA_IRQHandler                       0x08000fd5   Thumb Code  1756  stm32h7xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080016bd   Thumb Code   922  stm32h7xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_GPIO_Init                            0x08001a79   Thumb Code   496  stm32h7xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08001c95   Thumb Code    10  stm32h7xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08001ca1   Thumb Code     6  stm32h7xx_hal.o(i.HAL_GetTick)
    HAL_I2CEx_ConfigAnalogFilter             0x08001cad   Thumb Code    88  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter)
    HAL_I2CEx_ConfigDigitalFilter            0x08001d05   Thumb Code    84  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter)
    HAL_I2CEx_EnableFastModePlus             0x08001d59   Thumb Code    30  stm32h7xx_hal_i2c_ex.o(i.HAL_I2CEx_EnableFastModePlus)
    HAL_I2C_AbortCpltCallback                0x08001d81   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback)
    HAL_I2C_AddrCallback                     0x08001d83   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_AddrCallback)
    HAL_I2C_ER_IRQHandler                    0x08001d85   Thumb Code   104  stm32h7xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler)
    HAL_I2C_EV_IRQHandler                    0x08001ded   Thumb Code    16  stm32h7xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler)
    HAL_I2C_ErrorCallback                    0x08001dfd   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_ErrorCallback)
    HAL_I2C_Init                             0x08001e01   Thumb Code   186  stm32h7xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_ListenCpltCallback               0x08001ec1   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback)
    HAL_I2C_Master_Transmit                  0x08001ec5   Thumb Code   324  stm32h7xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_MspInit                          0x08002011   Thumb Code   350  i2c.o(i.HAL_I2C_MspInit)
    HAL_I2C_SlaveRxCpltCallback              0x08002191   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback)
    HAL_I2C_SlaveTxCpltCallback              0x08002193   Thumb Code     2  stm32h7xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback)
    HAL_IncTick                              0x08002195   Thumb Code    12  stm32h7xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x080021a5   Thumb Code    74  stm32h7xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002201   Thumb Code    56  stm32h7xx_hal.o(i.HAL_InitTick)
    HAL_MPU_ConfigRegion                     0x08002241   Thumb Code    86  stm32h7xx_hal_cortex.o(i.HAL_MPU_ConfigRegion)
    HAL_MPU_Disable                          0x0800229d   Thumb Code    24  stm32h7xx_hal_cortex.o(i.HAL_MPU_Disable)
    HAL_MPU_Enable                           0x080022b9   Thumb Code    30  stm32h7xx_hal_cortex.o(i.HAL_MPU_Enable)
    HAL_MspInit                              0x080022dd   Thumb Code    22  stm32h7xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x080022f9   Thumb Code    26  stm32h7xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002315   Thumb Code    60  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002355   Thumb Code    28  stm32h7xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_PWREx_ConfigSupply                   0x08002379   Thumb Code    78  stm32h7xx_hal_pwr_ex.o(i.HAL_PWREx_ConfigSupply)
    HAL_RCCEx_GetD3PCLK1Freq                 0x080023cd   Thumb Code    26  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetD3PCLK1Freq)
    HAL_RCCEx_GetPLL2ClockFreq               0x080023f1   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL2ClockFreq)
    HAL_RCCEx_GetPLL3ClockFreq               0x08002535   Thumb Code   296  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPLL3ClockFreq)
    HAL_RCCEx_PeriphCLKConfig                0x08002679   Thumb Code  2362  stm32h7xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig)
    HAL_RCC_ClockConfig                      0x08002fb5   Thumb Code   580  stm32h7xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08003211   Thumb Code    52  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08003255   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003279   Thumb Code    26  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x0800329d   Thumb Code   278  stm32h7xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080033d5   Thumb Code  1318  stm32h7xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSCFG_AnalogSwitchConfig            0x080038fd   Thumb Code    12  stm32h7xx_hal.o(i.HAL_SYSCFG_AnalogSwitchConfig)
    HAL_SYSTICK_Config                       0x0800390d   Thumb Code    38  stm32h7xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x08003935   Thumb Code   146  stm32h7xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080039ed   Thumb Code    98  stm32h7xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08003a51   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08003a79   Thumb Code   248  stm32h7xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08003b75   Thumb Code   182  stm32h7xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08003c31   Thumb Code    92  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_MspPostInit                      0x08003c99   Thumb Code    70  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08003ced   Thumb Code   292  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08003e11   Thumb Code    98  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08003e73   Thumb Code     2  stm32h7xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_DisableFifoMode               0x08003e75   Thumb Code    64  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_DisableFifoMode)
    HAL_UARTEx_RxEventCallback               0x08003eb5   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UARTEx_RxFifoFullCallback            0x08003eb7   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_RxFifoFullCallback)
    HAL_UARTEx_SetRxFifoThreshold            0x08003eb9   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetRxFifoThreshold)
    HAL_UARTEx_SetTxFifoThreshold            0x08003f05   Thumb Code    76  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_SetTxFifoThreshold)
    HAL_UARTEx_TxFifoEmptyCallback           0x08003f51   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_TxFifoEmptyCallback)
    HAL_UARTEx_WakeupCallback                0x08003f53   Thumb Code     2  stm32h7xx_hal_uart_ex.o(i.HAL_UARTEx_WakeupCallback)
    HAL_UART_ErrorCallback                   0x08003f55   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003f59   Thumb Code   898  stm32h7xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080042f5   Thumb Code   106  stm32h7xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004361   Thumb Code   350  usart.o(i.HAL_UART_MspInit)
    HAL_UART_TxCpltCallback                  0x080044dd   Thumb Code     2  stm32h7xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x080044df   Thumb Code     2  stm32h7xx_it.o(i.HardFault_Handler)
    I2C3_ER_IRQHandler                       0x080044e1   Thumb Code     6  stm32h7xx_it.o(i.I2C3_ER_IRQHandler)
    I2C3_EV_IRQHandler                       0x080044ed   Thumb Code     6  stm32h7xx_it.o(i.I2C3_EV_IRQHandler)
    MX_DMA_Init                              0x08004e21   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08004e4d   Thumb Code   156  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08004ef1   Thumb Code    76  i2c.o(i.MX_I2C1_Init)
    MX_I2C3_Init                             0x08004f49   Thumb Code    78  i2c.o(i.MX_I2C3_Init)
    MX_TIM1_Init                             0x08004fa5   Thumb Code   102  tim.o(i.MX_TIM1_Init)
    MX_TIM4_Init                             0x08005015   Thumb Code   150  tim.o(i.MX_TIM4_Init)
    MX_USART1_UART_Init                      0x080050b5   Thumb Code    98  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08005121   Thumb Code    94  usart.o(i.MX_USART2_UART_Init)
    MX_USART6_UART_Init                      0x08005189   Thumb Code    94  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x080051f1   Thumb Code     2  stm32h7xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x080051f3   Thumb Code     2  stm32h7xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x080051f5   Thumb Code    34  oled.o(i.OLED_Clear)
    OLED_ClearArea                           0x0800521d   Thumb Code   104  oled.o(i.OLED_ClearArea)
    OLED_GPIO_Init                           0x08005289   Thumb Code    22  oled.o(i.OLED_GPIO_Init)
    OLED_Init                                0x0800529f   Thumb Code   156  oled.o(i.OLED_Init)
    OLED_SetCursor                           0x0800533b   Thumb Code    34  oled.o(i.OLED_SetCursor)
    OLED_ShowChar                            0x0800535d   Thumb Code    52  oled.o(i.OLED_ShowChar)
    OLED_ShowImage                           0x08005399   Thumb Code   172  oled.o(i.OLED_ShowImage)
    OLED_ShowString                          0x08005449   Thumb Code    44  oled.o(i.OLED_ShowString)
    OLED_Update                              0x08005475   Thumb Code    34  oled.o(i.OLED_Update)
    OLED_WriteCommand                        0x0800549d   Thumb Code    28  oled.o(i.OLED_WriteCommand)
    OLED_WriteData                           0x080054bd   Thumb Code    62  oled.o(i.OLED_WriteData)
    PendSV_Handler                           0x08005501   Thumb Code     2  stm32h7xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08005745   Thumb Code     2  stm32h7xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08005747   Thumb Code     4  stm32h7xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x0800574d   Thumb Code   130  main.o(i.SystemClock_Config)
    SystemInit                               0x080057d5   Thumb Code   184  system_stm32h7xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x080058ad   Thumb Code   182  stm32h7xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x0800598d   Thumb Code    18  stm32h7xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08005a59   Thumb Code   126  stm32h7xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART_AdvFeatureConfig                    0x08005d75   Thumb Code   200  stm32h7xx_hal_uart.o(i.UART_AdvFeatureConfig)
    UART_CheckIdleState                      0x08005e3d   Thumb Code   170  stm32h7xx_hal_uart.o(i.UART_CheckIdleState)
    UART_SetConfig                           0x08005f4d   Thumb Code   914  stm32h7xx_hal_uart.o(i.UART_SetConfig)
    UART_WaitOnFlagUntilTimeout              0x08006329   Thumb Code   148  stm32h7xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    USART1_IRQHandler                        0x080063bd   Thumb Code     6  stm32h7xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x080063c9   Thumb Code     6  stm32h7xx_it.o(i.USART2_IRQHandler)
    UsageFault_Handler                       0x080063d5   Thumb Code     2  stm32h7xx_it.o(i.UsageFault_Handler)
    main                                     0x080063f9   Thumb Code   146  main.o(i.main)
    _fp_init                                 0x080064a1   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080064a9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080064a9   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    UARTPrescTable                           0x080064b2   Data          24  stm32h7xx_hal_uart.o(.constdata)
    D1CorePrescTable                         0x080064da   Data          16  system_stm32h7xx.o(.constdata)
    OLED_F8x16                               0x080064ea   Data        1520  oled_font.o(.constdata)
    OLED_F6x8                                0x08006ada   Data         570  oled_font.o(.constdata)
    Region$$Table$$Base                      0x08006d14   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006d34   Number         0  anon$$obj.o(Region$$Table)
    uwTickFreq                               0x24000000   Data           1  stm32h7xx_hal.o(.data)
    uwTickPrio                               0x24000004   Data           4  stm32h7xx_hal.o(.data)
    uwTick                                   0x24000008   Data           4  stm32h7xx_hal.o(.data)
    SystemCoreClock                          0x2400000c   Data           4  system_stm32h7xx.o(.data)
    SystemD2Clock                            0x24000010   Data           4  system_stm32h7xx.o(.data)
    hi2c1                                    0x24000014   Data          84  i2c.o(.bss)
    hi2c3                                    0x24000068   Data          84  i2c.o(.bss)
    hdma_i2c3_tx                             0x240000bc   Data         120  i2c.o(.bss)
    htim1                                    0x24000134   Data          76  tim.o(.bss)
    htim4                                    0x24000180   Data          76  tim.o(.bss)
    huart1                                   0x240001cc   Data         148  usart.o(.bss)
    huart2                                   0x24000260   Data         148  usart.o(.bss)
    huart6                                   0x240002f4   Data         148  usart.o(.bss)
    OLED_DisplayBuf                          0x24000388   Data        1024  oled.o(.bss)
    __libspace_start                         0x24000788   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x240007e8   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080002cd

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006d48, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00006d34, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000002cc   Data   RO            3    RESET               startup_stm32h723xx.o
    0x080002cc   0x080002cc   0x00000008   Code   RO         6578  * !!!main             c_w.l(__main.o)
    0x080002d4   0x080002d4   0x00000034   Code   RO         7120    !!!scatter          c_w.l(__scatter.o)
    0x08000308   0x08000308   0x0000001a   Code   RO         7122    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000322   0x08000322   0x00000002   PAD
    0x08000324   0x08000324   0x0000001c   Code   RO         7124    !!handler_zi        c_w.l(__scatter_zi.o)
    0x08000340   0x08000340   0x00000002   Code   RO         6977    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000342   0x08000342   0x00000004   Code   RO         6706    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000346   0x08000346   0x00000000   Code   RO         6709    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000346   0x08000346   0x00000008   Code   RO         6710    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6712    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6714    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6716    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6719    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6721    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6723    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6725    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6727    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6729    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6731    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6733    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6735    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6737    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6739    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6743    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6745    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6747    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000000   Code   RO         6749    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x0800034e   0x0800034e   0x00000002   Code   RO         6750    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000350   0x08000350   0x00000002   Code   RO         7093    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6979    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6981    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6983    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6986    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6989    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6991    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000000   Code   RO         6994    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000352   0x08000352   0x00000002   Code   RO         6995    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000354   0x08000354   0x00000000   Code   RO         6604    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000354   0x08000354   0x00000000   Code   RO         6803    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000354   0x08000354   0x00000006   Code   RO         6815    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800035a   0x0800035a   0x00000000   Code   RO         6805    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800035a   0x0800035a   0x00000004   Code   RO         6806    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x0800035e   0x0800035e   0x00000000   Code   RO         6808    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x0800035e   0x0800035e   0x00000008   Code   RO         6809    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x08000366   0x08000366   0x00000002   Code   RO         7002    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000368   0x08000368   0x00000000   Code   RO         7049    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000368   0x08000368   0x00000004   Code   RO         7050    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x0800036c   0x0800036c   0x00000006   Code   RO         7051    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000372   0x08000372   0x00000002   PAD
    0x08000374   0x08000374   0x00000000   Code   RO         6823    .emb_text           c_w.l(maybetermalloc1.o)
    0x08000374   0x08000374   0x00000048   Code   RO            4    .text               startup_stm32h723xx.o
    0x080003bc   0x080003bc   0x0000005e   Code   RO         6428    .text               c_w.l(h1_alloc.o)
    0x0800041a   0x0800041a   0x0000004e   Code   RO         6430    .text               c_w.l(h1_free.o)
    0x08000468   0x08000468   0x000000ee   Code   RO         6486    .text               c_w.l(lludivv7m.o)
    0x08000556   0x08000556   0x0000004e   Code   RO         6570    .text               c_w.l(rt_memclr_w.o)
    0x080005a4   0x080005a4   0x00000006   Code   RO         6576    .text               c_w.l(heapauxi.o)
    0x080005aa   0x080005aa   0x00000002   PAD
    0x080005ac   0x080005ac   0x00000008   Code   RO         6612    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x080005b4   0x080005b4   0x00000004   Code   RO         6619    .text               c_w.l(hguard.o)
    0x080005b8   0x080005b8   0x0000008a   Code   RO         6621    .text               c_w.l(init_alloc.o)
    0x08000642   0x08000642   0x0000000e   Code   RO         6625    .text               c_w.l(h1_init.o)
    0x08000650   0x08000650   0x00000008   Code   RO         6799    .text               c_w.l(libspace.o)
    0x08000658   0x08000658   0x00000034   Code   RO         6825    .text               c_w.l(h1_extend.o)
    0x0800068c   0x0800068c   0x0000000e   Code   RO         6865    .text               c_w.l(defsig_rtmem_outer.o)
    0x0800069a   0x0800069a   0x00000002   Code   RO         6893    .text               c_w.l(use_no_semi.o)
    0x0800069c   0x0800069c   0x00000000   Code   RO         6895    .text               c_w.l(indicate_semi.o)
    0x0800069c   0x0800069c   0x0000004a   Code   RO         6896    .text               c_w.l(sys_stackheap_outer.o)
    0x080006e6   0x080006e6   0x00000012   Code   RO         6954    .text               c_w.l(exit.o)
    0x080006f8   0x080006f8   0x0000000a   Code   RO         6956    .text               c_w.l(defsig_exit.o)
    0x08000702   0x08000702   0x00000002   PAD
    0x08000704   0x08000704   0x00000050   Code   RO         6966    .text               c_w.l(defsig_rtmem_inner.o)
    0x08000754   0x08000754   0x0000000c   Code   RO         7000    .text               c_w.l(sys_exit.o)
    0x08000760   0x08000760   0x00000032   Code   RO         7020    .text               c_w.l(defsig_general.o)
    0x08000792   0x08000792   0x0000000e   Code   RO         7046    .text               c_w.l(sys_wrch.o)
    0x080007a0   0x080007a0   0x00000002   Code   RO          405    i.BusFault_Handler  stm32h7xx_it.o
    0x080007a2   0x080007a2   0x00000002   PAD
    0x080007a4   0x080007a4   0x0000000c   Code   RO          406    i.DMA1_Stream0_IRQHandler  stm32h7xx_it.o
    0x080007b0   0x080007b0   0x000000b4   Code   RO         1308    i.DMA_CalcBaseAndBitshift  stm32h7xx_hal_dma.o
    0x08000864   0x08000864   0x0000009c   Code   RO         1309    i.DMA_CalcDMAMUXChannelBaseAndMask  stm32h7xx_hal_dma.o
    0x08000900   0x08000900   0x00000074   Code   RO         1310    i.DMA_CalcDMAMUXRequestGenBaseAndMask  stm32h7xx_hal_dma.o
    0x08000974   0x08000974   0x00000054   Code   RO         1311    i.DMA_CheckFifoParam  stm32h7xx_hal_dma.o
    0x080009c8   0x080009c8   0x00000002   Code   RO          407    i.DebugMon_Handler  stm32h7xx_it.o
    0x080009ca   0x080009ca   0x00000002   Code   RO           13    i.Error_Handler     main.o
    0x080009cc   0x080009cc   0x0000001c   Code   RO         4506    i.ExitRun0Mode      system_stm32h7xx.o
    0x080009e8   0x080009e8   0x00000360   Code   RO         1313    i.HAL_DMA_Abort     stm32h7xx_hal_dma.o
    0x08000d48   0x08000d48   0x00000284   Code   RO         1314    i.HAL_DMA_Abort_IT  stm32h7xx_hal_dma.o
    0x08000fcc   0x08000fcc   0x00000006   Code   RO         1317    i.HAL_DMA_GetState  stm32h7xx_hal_dma.o
    0x08000fd2   0x08000fd2   0x00000002   PAD
    0x08000fd4   0x08000fd4   0x000006e8   Code   RO         1318    i.HAL_DMA_IRQHandler  stm32h7xx_hal_dma.o
    0x080016bc   0x080016bc   0x000003bc   Code   RO         1319    i.HAL_DMA_Init      stm32h7xx_hal_dma.o
    0x08001a78   0x08001a78   0x0000021c   Code   RO         1161    i.HAL_GPIO_Init     stm32h7xx_hal_gpio.o
    0x08001c94   0x08001c94   0x0000000a   Code   RO         1165    i.HAL_GPIO_WritePin  stm32h7xx_hal_gpio.o
    0x08001c9e   0x08001c9e   0x00000002   PAD
    0x08001ca0   0x08001ca0   0x0000000c   Code   RO         2026    i.HAL_GetTick       stm32h7xx_hal.o
    0x08001cac   0x08001cac   0x00000058   Code   RO         2823    i.HAL_I2CEx_ConfigAnalogFilter  stm32h7xx_hal_i2c_ex.o
    0x08001d04   0x08001d04   0x00000054   Code   RO         2824    i.HAL_I2CEx_ConfigDigitalFilter  stm32h7xx_hal_i2c_ex.o
    0x08001d58   0x08001d58   0x00000028   Code   RO         2827    i.HAL_I2CEx_EnableFastModePlus  stm32h7xx_hal_i2c_ex.o
    0x08001d80   0x08001d80   0x00000002   Code   RO         2351    i.HAL_I2C_AbortCpltCallback  stm32h7xx_hal_i2c.o
    0x08001d82   0x08001d82   0x00000002   Code   RO         2352    i.HAL_I2C_AddrCallback  stm32h7xx_hal_i2c.o
    0x08001d84   0x08001d84   0x00000068   Code   RO         2355    i.HAL_I2C_ER_IRQHandler  stm32h7xx_hal_i2c.o
    0x08001dec   0x08001dec   0x00000010   Code   RO         2356    i.HAL_I2C_EV_IRQHandler  stm32h7xx_hal_i2c.o
    0x08001dfc   0x08001dfc   0x00000002   Code   RO         2358    i.HAL_I2C_ErrorCallback  stm32h7xx_hal_i2c.o
    0x08001dfe   0x08001dfe   0x00000002   PAD
    0x08001e00   0x08001e00   0x000000c0   Code   RO         2362    i.HAL_I2C_Init      stm32h7xx_hal_i2c.o
    0x08001ec0   0x08001ec0   0x00000002   Code   RO         2364    i.HAL_I2C_ListenCpltCallback  stm32h7xx_hal_i2c.o
    0x08001ec2   0x08001ec2   0x00000002   PAD
    0x08001ec4   0x08001ec4   0x0000014c   Code   RO         2375    i.HAL_I2C_Master_Transmit  stm32h7xx_hal_i2c.o
    0x08002010   0x08002010   0x00000180   Code   RO          238    i.HAL_I2C_MspInit   i2c.o
    0x08002190   0x08002190   0x00000002   Code   RO         2388    i.HAL_I2C_SlaveRxCpltCallback  stm32h7xx_hal_i2c.o
    0x08002192   0x08002192   0x00000002   Code   RO         2389    i.HAL_I2C_SlaveTxCpltCallback  stm32h7xx_hal_i2c.o
    0x08002194   0x08002194   0x00000010   Code   RO         2032    i.HAL_IncTick       stm32h7xx_hal.o
    0x080021a4   0x080021a4   0x0000005c   Code   RO         2033    i.HAL_Init          stm32h7xx_hal.o
    0x08002200   0x08002200   0x00000040   Code   RO         2034    i.HAL_InitTick      stm32h7xx_hal.o
    0x08002240   0x08002240   0x0000005c   Code   RO          536    i.HAL_MPU_ConfigRegion  stm32h7xx_hal_cortex.o
    0x0800229c   0x0800229c   0x0000001c   Code   RO          537    i.HAL_MPU_Disable   stm32h7xx_hal_cortex.o
    0x080022b8   0x080022b8   0x00000024   Code   RO          539    i.HAL_MPU_Enable    stm32h7xx_hal_cortex.o
    0x080022dc   0x080022dc   0x0000001c   Code   RO          511    i.HAL_MspInit       stm32h7xx_hal_msp.o
    0x080022f8   0x080022f8   0x0000001a   Code   RO          543    i.HAL_NVIC_EnableIRQ  stm32h7xx_hal_cortex.o
    0x08002312   0x08002312   0x00000002   PAD
    0x08002314   0x08002314   0x00000040   Code   RO          549    i.HAL_NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x08002354   0x08002354   0x00000024   Code   RO          550    i.HAL_NVIC_SetPriorityGrouping  stm32h7xx_hal_cortex.o
    0x08002378   0x08002378   0x00000054   Code   RO         1762    i.HAL_PWREx_ConfigSupply  stm32h7xx_hal_pwr_ex.o
    0x080023cc   0x080023cc   0x00000024   Code   RO          804    i.HAL_RCCEx_GetD3PCLK1Freq  stm32h7xx_hal_rcc_ex.o
    0x080023f0   0x080023f0   0x00000144   Code   RO          806    i.HAL_RCCEx_GetPLL2ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08002534   0x08002534   0x00000144   Code   RO          807    i.HAL_RCCEx_GetPLL3ClockFreq  stm32h7xx_hal_rcc_ex.o
    0x08002678   0x08002678   0x0000093a   Code   RO          813    i.HAL_RCCEx_PeriphCLKConfig  stm32h7xx_hal_rcc_ex.o
    0x08002fb2   0x08002fb2   0x00000002   PAD
    0x08002fb4   0x08002fb4   0x0000025c   Code   RO          689    i.HAL_RCC_ClockConfig  stm32h7xx_hal_rcc.o
    0x08003210   0x08003210   0x00000044   Code   RO          694    i.HAL_RCC_GetHCLKFreq  stm32h7xx_hal_rcc.o
    0x08003254   0x08003254   0x00000024   Code   RO          696    i.HAL_RCC_GetPCLK1Freq  stm32h7xx_hal_rcc.o
    0x08003278   0x08003278   0x00000024   Code   RO          697    i.HAL_RCC_GetPCLK2Freq  stm32h7xx_hal_rcc.o
    0x0800329c   0x0800329c   0x00000138   Code   RO          698    i.HAL_RCC_GetSysClockFreq  stm32h7xx_hal_rcc.o
    0x080033d4   0x080033d4   0x00000526   Code   RO          701    i.HAL_RCC_OscConfig  stm32h7xx_hal_rcc.o
    0x080038fa   0x080038fa   0x00000002   PAD
    0x080038fc   0x080038fc   0x00000010   Code   RO         2040    i.HAL_SYSCFG_AnalogSwitchConfig  stm32h7xx_hal.o
    0x0800390c   0x0800390c   0x00000026   Code   RO          554    i.HAL_SYSTICK_Config  stm32h7xx_hal_cortex.o
    0x08003932   0x08003932   0x00000002   PAD
    0x08003934   0x08003934   0x000000b8   Code   RO         3688    i.HAL_TIMEx_MasterConfigSynchronization  stm32h7xx_hal_tim_ex.o
    0x080039ec   0x080039ec   0x00000062   Code   RO         2951    i.HAL_TIM_Base_Init  stm32h7xx_hal_tim.o
    0x08003a4e   0x08003a4e   0x00000002   PAD
    0x08003a50   0x08003a50   0x00000028   Code   RO          286    i.HAL_TIM_Base_MspInit  tim.o
    0x08003a78   0x08003a78   0x000000fc   Code   RO         2960    i.HAL_TIM_ConfigClockSource  stm32h7xx_hal_tim.o
    0x08003b74   0x08003b74   0x000000bc   Code   RO         2972    i.HAL_TIM_Encoder_Init  stm32h7xx_hal_tim.o
    0x08003c30   0x08003c30   0x00000068   Code   RO          288    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08003c98   0x08003c98   0x00000054   Code   RO          289    i.HAL_TIM_MspPostInit  tim.o
    0x08003cec   0x08003cec   0x00000124   Code   RO         3023    i.HAL_TIM_PWM_ConfigChannel  stm32h7xx_hal_tim.o
    0x08003e10   0x08003e10   0x00000062   Code   RO         3026    i.HAL_TIM_PWM_Init  stm32h7xx_hal_tim.o
    0x08003e72   0x08003e72   0x00000002   Code   RO         3028    i.HAL_TIM_PWM_MspInit  stm32h7xx_hal_tim.o
    0x08003e74   0x08003e74   0x00000040   Code   RO         4388    i.HAL_UARTEx_DisableFifoMode  stm32h7xx_hal_uart_ex.o
    0x08003eb4   0x08003eb4   0x00000002   Code   RO         3977    i.HAL_UARTEx_RxEventCallback  stm32h7xx_hal_uart.o
    0x08003eb6   0x08003eb6   0x00000002   Code   RO         4396    i.HAL_UARTEx_RxFifoFullCallback  stm32h7xx_hal_uart_ex.o
    0x08003eb8   0x08003eb8   0x0000004c   Code   RO         4397    i.HAL_UARTEx_SetRxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x08003f04   0x08003f04   0x0000004c   Code   RO         4398    i.HAL_UARTEx_SetTxFifoThreshold  stm32h7xx_hal_uart_ex.o
    0x08003f50   0x08003f50   0x00000002   Code   RO         4400    i.HAL_UARTEx_TxFifoEmptyCallback  stm32h7xx_hal_uart_ex.o
    0x08003f52   0x08003f52   0x00000002   Code   RO         4401    i.HAL_UARTEx_WakeupCallback  stm32h7xx_hal_uart_ex.o
    0x08003f54   0x08003f54   0x00000002   Code   RO         3993    i.HAL_UART_ErrorCallback  stm32h7xx_hal_uart.o
    0x08003f56   0x08003f56   0x00000002   PAD
    0x08003f58   0x08003f58   0x0000039c   Code   RO         3996    i.HAL_UART_IRQHandler  stm32h7xx_hal_uart.o
    0x080042f4   0x080042f4   0x0000006a   Code   RO         3997    i.HAL_UART_Init     stm32h7xx_hal_uart.o
    0x0800435e   0x0800435e   0x00000002   PAD
    0x08004360   0x08004360   0x0000017c   Code   RO          352    i.HAL_UART_MspInit  usart.o
    0x080044dc   0x080044dc   0x00000002   Code   RO         4009    i.HAL_UART_TxCpltCallback  stm32h7xx_hal_uart.o
    0x080044de   0x080044de   0x00000002   Code   RO          408    i.HardFault_Handler  stm32h7xx_it.o
    0x080044e0   0x080044e0   0x0000000c   Code   RO          409    i.I2C3_ER_IRQHandler  stm32h7xx_it.o
    0x080044ec   0x080044ec   0x0000000c   Code   RO          410    i.I2C3_EV_IRQHandler  stm32h7xx_it.o
    0x080044f8   0x080044f8   0x00000014   Code   RO         2401    i.I2C_DMAAbort      stm32h7xx_hal_i2c.o
    0x0800450c   0x0800450c   0x00000060   Code   RO         2407    i.I2C_Disable_IRQ   stm32h7xx_hal_i2c.o
    0x0800456c   0x0800456c   0x00000022   Code   RO         2409    i.I2C_Flush_TXDR    stm32h7xx_hal_i2c.o
    0x0800458e   0x0800458e   0x00000094   Code   RO         2410    i.I2C_ITAddrCplt    stm32h7xx_hal_i2c.o
    0x08004622   0x08004622   0x00000002   PAD
    0x08004624   0x08004624   0x00000120   Code   RO         2411    i.I2C_ITError       stm32h7xx_hal_i2c.o
    0x08004744   0x08004744   0x00000064   Code   RO         2412    i.I2C_ITListenCplt  stm32h7xx_hal_i2c.o
    0x080047a8   0x080047a8   0x00000250   Code   RO         2415    i.I2C_ITSlaveCplt   stm32h7xx_hal_i2c.o
    0x080049f8   0x080049f8   0x00000072   Code   RO         2416    i.I2C_ITSlaveSeqCplt  stm32h7xx_hal_i2c.o
    0x08004a6a   0x08004a6a   0x00000002   PAD
    0x08004a6c   0x08004a6c   0x00000110   Code   RO         2417    i.I2C_IsErrorOccurred  stm32h7xx_hal_i2c.o
    0x08004b7c   0x08004b7c   0x00000118   Code   RO         2425    i.I2C_Slave_ISR_IT  stm32h7xx_hal_i2c.o
    0x08004c94   0x08004c94   0x00000030   Code   RO         2426    i.I2C_TransferConfig  stm32h7xx_hal_i2c.o
    0x08004cc4   0x08004cc4   0x0000002a   Code   RO         2427    i.I2C_TreatErrorCallback  stm32h7xx_hal_i2c.o
    0x08004cee   0x08004cee   0x0000007c   Code   RO         2428    i.I2C_WaitOnFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x08004d6a   0x08004d6a   0x00000058   Code   RO         2430    i.I2C_WaitOnSTOPFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x08004dc2   0x08004dc2   0x0000005c   Code   RO         2431    i.I2C_WaitOnTXISFlagUntilTimeout  stm32h7xx_hal_i2c.o
    0x08004e1e   0x08004e1e   0x00000002   PAD
    0x08004e20   0x08004e20   0x0000002c   Code   RO          213    i.MX_DMA_Init       dma.o
    0x08004e4c   0x08004e4c   0x000000a4   Code   RO          189    i.MX_GPIO_Init      gpio.o
    0x08004ef0   0x08004ef0   0x00000058   Code   RO          239    i.MX_I2C1_Init      i2c.o
    0x08004f48   0x08004f48   0x0000005c   Code   RO          240    i.MX_I2C3_Init      i2c.o
    0x08004fa4   0x08004fa4   0x00000070   Code   RO          290    i.MX_TIM1_Init      tim.o
    0x08005014   0x08005014   0x000000a0   Code   RO          291    i.MX_TIM4_Init      tim.o
    0x080050b4   0x080050b4   0x0000006c   Code   RO          353    i.MX_USART1_UART_Init  usart.o
    0x08005120   0x08005120   0x00000068   Code   RO          354    i.MX_USART2_UART_Init  usart.o
    0x08005188   0x08005188   0x00000068   Code   RO          355    i.MX_USART6_UART_Init  usart.o
    0x080051f0   0x080051f0   0x00000002   Code   RO          411    i.MemManage_Handler  stm32h7xx_it.o
    0x080051f2   0x080051f2   0x00000002   Code   RO          412    i.NMI_Handler       stm32h7xx_it.o
    0x080051f4   0x080051f4   0x00000028   Code   RO         4733    i.OLED_Clear        oled.o
    0x0800521c   0x0800521c   0x0000006c   Code   RO         4734    i.OLED_ClearArea    oled.o
    0x08005288   0x08005288   0x00000016   Code   RO         4742    i.OLED_GPIO_Init    oled.o
    0x0800529e   0x0800529e   0x0000009c   Code   RO         4747    i.OLED_Init         oled.o
    0x0800533a   0x0800533a   0x00000022   Code   RO         4753    i.OLED_SetCursor    oled.o
    0x0800535c   0x0800535c   0x0000003c   Code   RO         4755    i.OLED_ShowChar     oled.o
    0x08005398   0x08005398   0x000000b0   Code   RO         4759    i.OLED_ShowImage    oled.o
    0x08005448   0x08005448   0x0000002c   Code   RO         4762    i.OLED_ShowString   oled.o
    0x08005474   0x08005474   0x00000028   Code   RO         4763    i.OLED_Update       oled.o
    0x0800549c   0x0800549c   0x00000020   Code   RO         4765    i.OLED_WriteCommand  oled.o
    0x080054bc   0x080054bc   0x00000044   Code   RO         4766    i.OLED_WriteData    oled.o
    0x08005500   0x08005500   0x00000002   Code   RO          413    i.PendSV_Handler    stm32h7xx_it.o
    0x08005502   0x08005502   0x00000002   PAD
    0x08005504   0x08005504   0x00000120   Code   RO          816    i.RCCEx_PLL2_Config  stm32h7xx_hal_rcc_ex.o
    0x08005624   0x08005624   0x00000120   Code   RO          817    i.RCCEx_PLL3_Config  stm32h7xx_hal_rcc_ex.o
    0x08005744   0x08005744   0x00000002   Code   RO          414    i.SVC_Handler       stm32h7xx_it.o
    0x08005746   0x08005746   0x00000004   Code   RO          415    i.SysTick_Handler   stm32h7xx_it.o
    0x0800574a   0x0800574a   0x00000002   PAD
    0x0800574c   0x0800574c   0x00000088   Code   RO           14    i.SystemClock_Config  main.o
    0x080057d4   0x080057d4   0x000000d8   Code   RO         4508    i.SystemInit        system_stm32h7xx.o
    0x080058ac   0x080058ac   0x000000e0   Code   RO         3044    i.TIM_Base_SetConfig  stm32h7xx_hal_tim.o
    0x0800598c   0x0800598c   0x00000012   Code   RO         3055    i.TIM_ETR_SetConfig  stm32h7xx_hal_tim.o
    0x0800599e   0x0800599e   0x00000002   PAD
    0x080059a0   0x080059a0   0x00000014   Code   RO         3056    i.TIM_ITRx_SetConfig  stm32h7xx_hal_tim.o
    0x080059b4   0x080059b4   0x000000a4   Code   RO         3057    i.TIM_OC1_SetConfig  stm32h7xx_hal_tim.o
    0x08005a58   0x08005a58   0x00000098   Code   RO         3058    i.TIM_OC2_SetConfig  stm32h7xx_hal_tim.o
    0x08005af0   0x08005af0   0x00000098   Code   RO         3059    i.TIM_OC3_SetConfig  stm32h7xx_hal_tim.o
    0x08005b88   0x08005b88   0x00000078   Code   RO         3060    i.TIM_OC4_SetConfig  stm32h7xx_hal_tim.o
    0x08005c00   0x08005c00   0x00000074   Code   RO         3061    i.TIM_OC5_SetConfig  stm32h7xx_hal_tim.o
    0x08005c74   0x08005c74   0x00000074   Code   RO         3062    i.TIM_OC6_SetConfig  stm32h7xx_hal_tim.o
    0x08005ce8   0x08005ce8   0x00000022   Code   RO         3064    i.TIM_TI1_ConfigInputStage  stm32h7xx_hal_tim.o
    0x08005d0a   0x08005d0a   0x00000024   Code   RO         3066    i.TIM_TI2_ConfigInputStage  stm32h7xx_hal_tim.o
    0x08005d2e   0x08005d2e   0x00000002   PAD
    0x08005d30   0x08005d30   0x00000044   Code   RO         4402    i.UARTEx_SetNbDataToProcess  stm32h7xx_hal_uart_ex.o
    0x08005d74   0x08005d74   0x000000c8   Code   RO         4011    i.UART_AdvFeatureConfig  stm32h7xx_hal_uart.o
    0x08005e3c   0x08005e3c   0x000000aa   Code   RO         4012    i.UART_CheckIdleState  stm32h7xx_hal_uart.o
    0x08005ee6   0x08005ee6   0x00000010   Code   RO         4013    i.UART_DMAAbortOnError  stm32h7xx_hal_uart.o
    0x08005ef6   0x08005ef6   0x00000002   PAD
    0x08005ef8   0x08005ef8   0x00000054   Code   RO         4023    i.UART_EndRxTransfer  stm32h7xx_hal_uart.o
    0x08005f4c   0x08005f4c   0x000003dc   Code   RO         4029    i.UART_SetConfig    stm32h7xx_hal_uart.o
    0x08006328   0x08006328   0x00000094   Code   RO         4036    i.UART_WaitOnFlagUntilTimeout  stm32h7xx_hal_uart.o
    0x080063bc   0x080063bc   0x0000000c   Code   RO          416    i.USART1_IRQHandler  stm32h7xx_it.o
    0x080063c8   0x080063c8   0x0000000c   Code   RO          417    i.USART2_IRQHandler  stm32h7xx_it.o
    0x080063d4   0x080063d4   0x00000002   Code   RO          418    i.UsageFault_Handler  stm32h7xx_it.o
    0x080063d6   0x080063d6   0x00000022   Code   RO          556    i.__NVIC_SetPriority  stm32h7xx_hal_cortex.o
    0x080063f8   0x080063f8   0x000000a8   Code   RO           15    i.main              main.o
    0x080064a0   0x080064a0   0x0000000a   Code   RO         6880    x$fpl$fpinit        fz_wv.l(fpinit.o)
    0x080064aa   0x080064aa   0x00000008   Data   RO         1325    .constdata          stm32h7xx_hal_dma.o
    0x080064b2   0x080064b2   0x00000018   Data   RO         4037    .constdata          stm32h7xx_hal_uart.o
    0x080064ca   0x080064ca   0x00000010   Data   RO         4403    .constdata          stm32h7xx_hal_uart_ex.o
    0x080064da   0x080064da   0x00000010   Data   RO         4509    .constdata          system_stm32h7xx.o
    0x080064ea   0x080064ea   0x000005f0   Data   RO         4961    .constdata          oled_font.o
    0x08006ada   0x08006ada   0x0000023a   Data   RO         4962    .constdata          oled_font.o
    0x08006d14   0x08006d14   0x00000020   Data   RO         7118    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08006d34, Size: 0x00000000, Max: 0x00020000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM2 (Exec base: 0x24000000, Load base: 0x08006d34, Size: 0x00000de8, Max: 0x00050000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x24000000   0x08006d34   0x0000000c   Data   RW         2057    .data               stm32h7xx_hal.o
    0x2400000c   0x08006d40   0x00000008   Data   RW         4510    .data               system_stm32h7xx.o
    0x24000014        -       0x00000120   Zero   RW          241    .bss                i2c.o
    0x24000134        -       0x00000098   Zero   RW          292    .bss                tim.o
    0x240001cc        -       0x000001bc   Zero   RW          356    .bss                usart.o
    0x24000388        -       0x00000400   Zero   RW         4768    .bss                oled.o
    0x24000788        -       0x00000060   Zero   RW         6800    .bss                c_w.l(libspace.o)
    0x240007e8        -       0x00000200   Zero   RW            2    HEAP                startup_stm32h723xx.o
    0x240009e8        -       0x00000400   Zero   RW            1    STACK               startup_stm32h723xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        44          4          0          0          0        862   dma.o
       164          8          0          0          0       1095   gpio.o
       564         60          0          0        288       2927   i2c.o
       306         28          0          0          0    1200800   main.o
       780         38          0          0       1024       8800   oled.o
         0          0       2090          0          0       1218   oled_font.o
        72         30        716          0       1536        892   startup_stm32h723xx.o
       200         40          0         12          0      23989   stm32h7xx_hal.o
       354         28          0          0          0      42185   stm32h7xx_hal_cortex.o
      4774        176          8          0          0      10830   stm32h7xx_hal_dma.o
       550         44          0          0          0       2336   stm32h7xx_hal_gpio.o
      2994         84          0          0          0      23377   stm32h7xx_hal_i2c.o
       212         10          0          0          0       2814   stm32h7xx_hal_i2c_ex.o
        28          6          0          0          0        902   stm32h7xx_hal_msp.o
        84          6          0          0          0        833   stm32h7xx_hal_pwr_ex.o
      2374        102          0          0          0       7324   stm32h7xx_hal_rcc.o
      3622        112          0          0          0       8009   stm32h7xx_hal_rcc_ex.o
      2082        216          0          0          0      14947   stm32h7xx_hal_tim.o
       184         38          0          0          0       1568   stm32h7xx_hal_tim_ex.o
      2642        148         24          0          0      30496   stm32h7xx_hal_uart.o
       290          6         16          0          0       5754   stm32h7xx_hal_uart_ex.o
        80         30          0          0          0       7122   stm32h7xx_it.o
       244         38         16          8          0       2049   system_stm32h7xx.o
       500         56          0          0        152       4083   tim.o
       696         60          0          0        444       3688   usart.o

    ----------------------------------------------------------------------
     23880       <USER>       <GROUP>         20       3444    1408900   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        40          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        18          0          0          0          0         80   exit.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
         2          0          0          0          0          0   libinit.o
        14          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       238          0          0          0          0        100   lludivv7m.o
         0          0          0          0          0          0   maybetermalloc1.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        10          0          0          0          0        116   fpinit.o

    ----------------------------------------------------------------------
      1174         <USER>          <GROUP>          0         96       1952   Library Totals
         8          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      1156         78          0          0         96       1836   c_w.l
        10          0          0          0          0        116   fz_wv.l

    ----------------------------------------------------------------------
      1174         <USER>          <GROUP>          0         96       1952   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     25054       1446       2902         20       3540    1396216   Grand Totals
     25054       1446       2902         20       3540    1396216   ELF Image Totals
     25054       1446       2902         20          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                27956 (  27.30kB)
    Total RW  Size (RW Data + ZI Data)              3560 (   3.48kB)
    Total ROM Size (Code + RO Data + RW Data)      27976 (  27.32kB)

==============================================================================

