


ARM Macro Assembler    Page 1 


    1 00000000         ;*******************************************************
                       *************************
    2 00000000         ;* File Name          : startup_stm32h723xx.s
    3 00000000         ;* <AUTHOR> Application Team
    4 00000000         ;* Description        : STM32H7xx devices vector table f
                       or MDK-ARM toolchain. 
    5 00000000         ;*                      This module performs:
    6 00000000         ;*                      - Set the initial SP
    7 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
    8 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
    9 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   10 00000000         ;*                        calls main()).
   11 00000000         ;*                      After Reset the Cortex-M process
                       or is in Thread mode,
   12 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   13 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   14 00000000         ;*******************************************************
                       ***********************
   15 00000000         ;* @attention
   16 00000000         ;*
   17 00000000         ;* Copyright (c) 2019 STMicroelectronics.
   18 00000000         ;* All rights reserved.
   19 00000000         ;*
   20 00000000         ;* This software is licensed under terms that can be fou
                       nd in the LICENSE file
   21 00000000         ;* in the root directory of this software component.
   22 00000000         ;* If no LICENSE file comes with this software, it is pr
                       ovided AS-IS.
   23 00000000         ;*
   24 00000000         ;*******************************************************
                       ************************
   25 00000000         
   26 00000000         ; Amount of memory (in bytes) allocated for Stack
   27 00000000         ; Tailor this value to your application needs
   28 00000000         ; <h> Stack Configuration
   29 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   30 00000000         ; </h>
   31 00000000         
   32 00000000 00000400 
                       Stack_Size
                               EQU              0x400
   33 00000000         
   34 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   35 00000000         Stack_Mem
                               SPACE            Stack_Size
   36 00000400         __initial_sp
   37 00000400         
   38 00000400         
   39 00000400         ; <h> Heap Configuration
   40 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000400         ; </h>
   42 00000400         
   43 00000400 00000200 
                       Heap_Size



ARM Macro Assembler    Page 2 


                               EQU              0x200
   44 00000400         
   45 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   46 00000000         __heap_base
   47 00000000         Heap_Mem
                               SPACE            Heap_Size
   48 00000200         __heap_limit
   49 00000200         
   50 00000200                 PRESERVE8
   51 00000200                 THUMB
   52 00000200         
   53 00000200         
   54 00000200         ; Vector Table Mapped to Address 0 at Reset
   55 00000200                 AREA             RESET, DATA, READONLY
   56 00000000                 EXPORT           __Vectors
   57 00000000                 EXPORT           __Vectors_End
   58 00000000                 EXPORT           __Vectors_Size
   59 00000000         
   60 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   61 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   62 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   63 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   64 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   65 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   66 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   67 0000001C 00000000        DCD              0           ; Reserved
   68 00000020 00000000        DCD              0           ; Reserved
   69 00000024 00000000        DCD              0           ; Reserved
   70 00000028 00000000        DCD              0           ; Reserved
   71 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   72 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   73 00000034 00000000        DCD              0           ; Reserved
   74 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   75 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   76 00000040         
   77 00000040         ; External Interrupts
   78 00000040 00000000        DCD              WWDG_IRQHandler ; Window WatchD
                                                            og interrupt ( wwdg
                                                            1_it)              
                                                                               
                                                                    
   79 00000044 00000000        DCD              PVD_AVD_IRQHandler ; PVD/AVD th
                                                            rough EXTI Line det
                                                            ection             
                                                                       
   80 00000048 00000000        DCD              TAMP_STAMP_IRQHandler ; Tamper 
                                                            and TimeStamps thro



ARM Macro Assembler    Page 3 


                                                            ugh the EXTI line  
                                                                      
   81 0000004C 00000000        DCD              RTC_WKUP_IRQHandler ; RTC Wakeu
                                                            p through the EXTI 
                                                            line               
                                                                    
   82 00000050 00000000        DCD              FLASH_IRQHandler ; FLASH       
                                                                               
                                                                             
   83 00000054 00000000        DCD              RCC_IRQHandler ; RCC           
                                                                               
                                                                           
   84 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line0  
                                                                               
                                                                               
                                                                 
   85 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line1  
                                                                               
                                                                               
                                                                 
   86 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line2  
                                                                               
                                                                               
                                                                 
   87 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line3  
                                                                               
                                                                               
                                                                 
   88 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line4 
   89 0000006C 00000000        DCD              DMA1_Stream0_IRQHandler 
                                                            ; DMA1 Stream 0
   90 00000070 00000000        DCD              DMA1_Stream1_IRQHandler ; DMA1 
                                                            Stream 1           
                                                                               
                                                                 
   91 00000074 00000000        DCD              DMA1_Stream2_IRQHandler ; DMA1 
                                                            Stream 2           
                                                                               
                                                                 
   92 00000078 00000000        DCD              DMA1_Stream3_IRQHandler ; DMA1 
                                                            Stream 3           
                                                                               
                                                                 
   93 0000007C 00000000        DCD              DMA1_Stream4_IRQHandler ; DMA1 
                                                            Stream 4           
                                                                               
                                                                 
   94 00000080 00000000        DCD              DMA1_Stream5_IRQHandler ; DMA1 
                                                            Stream 5           
                                                                               
                                                                 
   95 00000084 00000000        DCD              DMA1_Stream6_IRQHandler 
                                                            ; DMA1 Stream 6  
   96 00000088 00000000        DCD              ADC_IRQHandler ; ADC1, ADC2    
                                                                               
                                                                  
   97 0000008C 00000000        DCD              FDCAN1_IT0_IRQHandler ; FDCAN1 
                                                            interrupt line 0   
                                                                               



ARM Macro Assembler    Page 4 


                                                              
   98 00000090 00000000        DCD              FDCAN2_IT0_IRQHandler ; FDCAN2 
                                                            interrupt line 0   
                                                                               
                                                                     
   99 00000094 00000000        DCD              FDCAN1_IT1_IRQHandler ; FDCAN1 
                                                            interrupt line 1   
                                                                               
                                                              
  100 00000098 00000000        DCD              FDCAN2_IT1_IRQHandler ; FDCAN2 
                                                            interrupt line 1   
                                                                               
                                                                               
                                                                  
  101 0000009C 00000000        DCD              EXTI9_5_IRQHandler ; External L
                                                            ine[9:5]s          
                                                                               
                                                                   
  102 000000A0 00000000        DCD              TIM1_BRK_IRQHandler ; TIM1 Brea
                                                            k interrupt        
                                                                       
  103 000000A4 00000000        DCD              TIM1_UP_IRQHandler ; TIM1 Updat
                                                            e Interrupt        
                                                                     
  104 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion Interrupt 
  105 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare         
                                                                               
                                                                   
  106 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2         
                                                                               
                                                                            
  107 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3         
                                                                               
                                                                            
  108 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4         
                                                                               
                                                                            
  109 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                                               
                                                                               
                                                                   
  110 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                                               
                                                                               
                                                                   
  111 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                                               
                                                                               
                                                                   
  112 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                                               
                                                                               
                                                                     
  113 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1         
                                                                               
                                                                            



ARM Macro Assembler    Page 5 


  114 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2         
                                                                               
                                                                            
  115 000000D4 00000000        DCD              USART1_IRQHandler ; USART1     
                                                                               
                                                                              
  116 000000D8 00000000        DCD              USART2_IRQHandler ; USART2     
                                                                               
                                                                              
  117 000000DC 00000000        DCD              USART3_IRQHandler ; USART3     
                                                                               
                                                                              
  118 000000E0 00000000        DCD              EXTI15_10_IRQHandler ; External
                                                             Line[15:10]  
  119 000000E4 00000000        DCD              RTC_Alarm_IRQHandler ; RTC Alar
                                                            m (A and B) through
                                                             EXTI Line
  120 000000E8 00000000        DCD              0           ; Reserved         
                                                                               
                                                                          
  121 000000EC 00000000        DCD              TIM8_BRK_TIM12_IRQHandler ; TIM
                                                            8 Break Interrupt a
                                                            nd TIM12 global int
                                                            errupt             
                                                                
  122 000000F0 00000000        DCD              TIM8_UP_TIM13_IRQHandler ; TIM8
                                                             Update Interrupt a
                                                            nd TIM13 global int
                                                            errupt
  123 000000F4 00000000        DCD              TIM8_TRG_COM_TIM14_IRQHandler ;
                                                             TIM8 Trigger and C
                                                            ommutation Interrup
                                                            t and TIM14 global 
                                                            interrupt
  124 000000F8 00000000        DCD              TIM8_CC_IRQHandler ; TIM8 Captu
                                                            re Compare Interrup
                                                            t
  125 000000FC 00000000        DCD              DMA1_Stream7_IRQHandler ; DMA1 
                                                            Stream7            
                                                                               
                                                                        
  126 00000100 00000000        DCD              FMC_IRQHandler ; FMC           
                                                                              
  127 00000104 00000000        DCD              SDMMC1_IRQHandler ; SDMMC1     
                                                                               
                                                                
  128 00000108 00000000        DCD              TIM5_IRQHandler ; TIM5         
                                                                               
                                                            
  129 0000010C 00000000        DCD              SPI3_IRQHandler ; SPI3         
                                                                               
                                                            
  130 00000110 00000000        DCD              UART4_IRQHandler ; UART4       
                                                                               
                                                             
  131 00000114 00000000        DCD              UART5_IRQHandler ; UART5       
                                                                               
                                                             
  132 00000118 00000000        DCD              TIM6_DAC_IRQHandler ; TIM6 and 



ARM Macro Assembler    Page 6 


                                                            DAC1&2 underrun err
                                                            ors           
  133 0000011C 00000000        DCD              TIM7_IRQHandler 
                                                            ; TIM7           
  134 00000120 00000000        DCD              DMA2_Stream0_IRQHandler ; DMA2 
                                                            Stream 0           
                                                                    
  135 00000124 00000000        DCD              DMA2_Stream1_IRQHandler ; DMA2 
                                                            Stream 1           
                                                                    
  136 00000128 00000000        DCD              DMA2_Stream2_IRQHandler ; DMA2 
                                                            Stream 2           
                                                                    
  137 0000012C 00000000        DCD              DMA2_Stream3_IRQHandler ; DMA2 
                                                            Stream 3           
                                                                    
  138 00000130 00000000        DCD              DMA2_Stream4_IRQHandler ; DMA2 
                                                            Stream 4           
                                                                    
  139 00000134 00000000        DCD              ETH_IRQHandler ; Ethernet      
                                                                              
  140 00000138 00000000        DCD              ETH_WKUP_IRQHandler ; Ethernet 
                                                            Wakeup through EXTI
                                                             line              
                                                            
  141 0000013C 00000000        DCD              FDCAN_CAL_IRQHandler ; FDCAN ca
                                                            libration unit inte
                                                            rrupt              
                                                                      
  142 00000140 00000000        DCD              0           ; Reserved         
                                                                               
                                                              
  143 00000144 00000000        DCD              0           ; Reserved 
  144 00000148 00000000        DCD              0           ; Reserved 
  145 0000014C 00000000        DCD              0           ; Reserved         
                                                                         
  146 00000150 00000000        DCD              DMA2_Stream5_IRQHandler ; DMA2 
                                                            Stream 5           
                                                                    
  147 00000154 00000000        DCD              DMA2_Stream6_IRQHandler ; DMA2 
                                                            Stream 6           
                                                                    
  148 00000158 00000000        DCD              DMA2_Stream7_IRQHandler ; DMA2 
                                                            Stream 7           
                                                                    
  149 0000015C 00000000        DCD              USART6_IRQHandler ; USART6     
                                                                               
                                                               
  150 00000160 00000000        DCD              I2C3_EV_IRQHandler ; I2C3 event
                                                                               
                                                                      
  151 00000164 00000000        DCD              I2C3_ER_IRQHandler ; I2C3 error
                                                                               
                                                                      
  152 00000168 00000000        DCD              OTG_HS_EP1_OUT_IRQHandler ; USB
                                                             OTG HS End Point 1
                                                             Out               
                                                                   
  153 0000016C 00000000        DCD              OTG_HS_EP1_IN_IRQHandler ; USB 



ARM Macro Assembler    Page 7 


                                                            OTG HS End Point 1 
                                                            In                 
                                                                  
  154 00000170 00000000        DCD              OTG_HS_WKUP_IRQHandler ; USB OT
                                                            G HS Wakeup through
                                                             EXTI              
                                                                       
  155 00000174 00000000        DCD              OTG_HS_IRQHandler ; USB OTG HS 
                                                                               
                                                            
  156 00000178 00000000        DCD              DCMI_PSSI_IRQHandler ; DCMI, PS
                                                            SI                 
                                                                       
  157 0000017C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                     
  158 00000180 00000000        DCD              RNG_IRQHandler ; Rng
  159 00000184 00000000        DCD              FPU_IRQHandler ; FPU
  160 00000188 00000000        DCD              UART7_IRQHandler ; UART7
  161 0000018C 00000000        DCD              UART8_IRQHandler ; UART8
  162 00000190 00000000        DCD              SPI4_IRQHandler ; SPI4
  163 00000194 00000000        DCD              SPI5_IRQHandler ; SPI5
  164 00000198 00000000        DCD              SPI6_IRQHandler ; SPI6
  165 0000019C 00000000        DCD              SAI1_IRQHandler ; SAI1
  166 000001A0 00000000        DCD              LTDC_IRQHandler ; LTDC
  167 000001A4 00000000        DCD              LTDC_ER_IRQHandler ; LTDC error
                                                            
  168 000001A8 00000000        DCD              DMA2D_IRQHandler ; DMA2D
  169 000001AC 00000000        DCD              0           ; Reserved
  170 000001B0 00000000        DCD              OCTOSPI1_IRQHandler ; OCTOSPI1
  171 000001B4 00000000        DCD              LPTIM1_IRQHandler ; LPTIM1
  172 000001B8 00000000        DCD              CEC_IRQHandler ; HDMI_CEC
  173 000001BC 00000000        DCD              I2C4_EV_IRQHandler ; I2C4 Event
                                                                               
                                                                      
  174 000001C0 00000000        DCD              I2C4_ER_IRQHandler 
                                                            ; I2C4 Error 
  175 000001C4 00000000        DCD              SPDIF_RX_IRQHandler ; SPDIF_RX
  176 000001C8 00000000        DCD              0           ; Reserved         
                                                                        
  177 000001CC 00000000        DCD              0           ; Reserved         
                                                                          
  178 000001D0 00000000        DCD              0           ; Reserved         
                                                                            
  179 000001D4 00000000        DCD              0           ; Reserved         
                                                                    
  180 000001D8 00000000        DCD              DMAMUX1_OVR_IRQHandler ; DMAMUX
                                                            1 Overrun interrupt
                                                              
  181 000001DC 00000000        DCD              0           ; Reserved         
                                                                               
                                                              
  182 000001E0 00000000        DCD              0           ; Reserved         
                                                                               
                                                                    
  183 000001E4 00000000        DCD              0           ; Reserved         
                                                                               
                                                                   
  184 000001E8 00000000        DCD              0           ; Reserved         



ARM Macro Assembler    Page 8 


                                                                               
                                                                   
  185 000001EC 00000000        DCD              0           ; Reserved         
                                                                               
                                                                    
  186 000001F0 00000000        DCD              0           ; Reserved         
                                                                               
                                                                    
  187 000001F4 00000000        DCD              0           ; Reserved 
  188 000001F8 00000000        DCD              DFSDM1_FLT0_IRQHandler ; DFSDM 
                                                            Filter0 Interrupt  
                                                             
  189 000001FC 00000000        DCD              DFSDM1_FLT1_IRQHandler ; DFSDM 
                                                            Filter1 Interrupt  
                                                                               
                                                                               
                                                                
  190 00000200 00000000        DCD              DFSDM1_FLT2_IRQHandler ; DFSDM 
                                                            Filter2 Interrupt  
                                                                               
                                                                               
                                                                
  191 00000204 00000000        DCD              DFSDM1_FLT3_IRQHandler ; DFSDM 
                                                            Filter3 Interrupt  
                                                                               
                                                                               
                                                                               
                                                                               
                                                                  
  192 00000208 00000000        DCD              0           ; Reserved         
                                                                               
                                                                            
  193 0000020C 00000000        DCD              SWPMI1_IRQHandler ; Serial Wire
                                                             Interface 1 global
                                                             interrupt         
                                                                             
  194 00000210 00000000        DCD              TIM15_IRQHandler ; TIM15 global
                                                             Interrupt         
                                                                               
                                                                            
  195 00000214 00000000        DCD              TIM16_IRQHandler ; TIM16 global
                                                             Interrupt         
                                                                               
                                                                            
  196 00000218 00000000        DCD              TIM17_IRQHandler ; TIM17 global
                                                             Interrupt         
                                                                               
                                                                            
  197 0000021C 00000000        DCD              MDIOS_WKUP_IRQHandler ; MDIOS W
                                                            akeup  Interrupt   
                                                                               
                                                                               
                                                              
  198 00000220 00000000        DCD              MDIOS_IRQHandler ; MDIOS global
                                                             Interrupt         
                                                                               
                                                                            
  199 00000224 00000000        DCD              0           ; Reserved         
                                                                               



ARM Macro Assembler    Page 9 


                                                                             
  200 00000228 00000000        DCD              MDMA_IRQHandler ; MDMA global I
                                                            nterrupt           
                                                                               
                                                                           
  201 0000022C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                               
                                                            
  202 00000230 00000000        DCD              SDMMC2_IRQHandler ; SDMMC2 glob
                                                            al Interrupt       
                                                                               
                                                                             
  203 00000234 00000000        DCD              HSEM1_IRQHandler ; HSEM1 global
                                                             Interrupt         
                                                                               
                                                                             
  204 00000238 00000000        DCD              0           ; Reserved         
                                                                               
                                                                              
  205 0000023C 00000000        DCD              ADC3_IRQHandler ; ADC3 global I
                                                            nterrupt           
                                                                               
                                                                            
  206 00000240 00000000        DCD              DMAMUX2_OVR_IRQHandler ; DMAMUX
                                                             Overrun interrupt 
                                                                               
                                                                               
                                                                
  207 00000244 00000000        DCD              BDMA_Channel0_IRQHandler ; BDMA
                                                             Channel 0 global I
                                                            nterrupt           
                                                                               
                                                                  
  208 00000248 00000000        DCD              BDMA_Channel1_IRQHandler ; BDMA
                                                             Channel 1 global I
                                                            nterrupt           
                                                                               
                                                                  
  209 0000024C 00000000        DCD              BDMA_Channel2_IRQHandler ; BDMA
                                                             Channel 2 global I
                                                            nterrupt           
                                                                               
                                                                  
  210 00000250 00000000        DCD              BDMA_Channel3_IRQHandler ; BDMA
                                                             Channel 3 global I
                                                            nterrupt           
                                                                               
                                                                  
  211 00000254 00000000        DCD              BDMA_Channel4_IRQHandler ; BDMA
                                                             Channel 4 global I
                                                            nterrupt           
                                                                               
                                                                  
  212 00000258 00000000        DCD              BDMA_Channel5_IRQHandler ; BDMA
                                                             Channel 5 global I
                                                            nterrupt           
                                                                               
                                                                  



ARM Macro Assembler    Page 10 


  213 0000025C 00000000        DCD              BDMA_Channel6_IRQHandler ; BDMA
                                                             Channel 6 global I
                                                            nterrupt           
                                                                               
                                                                  
  214 00000260 00000000        DCD              BDMA_Channel7_IRQHandler ; BDMA
                                                             Channel 7 global I
                                                            nterrupt           
                                                                               
                                                                  
  215 00000264 00000000        DCD              COMP1_IRQHandler ; COMP1 global
                                                             Interrupt         
                                                                               
                                                                            
  216 00000268 00000000        DCD              LPTIM2_IRQHandler ; LP TIM2 glo
                                                            bal interrupt      
                                                                               
                                                                             
  217 0000026C 00000000        DCD              LPTIM3_IRQHandler ; LP TIM3 glo
                                                            bal interrupt      
                                                                               
                                                                             
  218 00000270 00000000        DCD              LPTIM4_IRQHandler ; LP TIM4 glo
                                                            bal interrupt      
                                                                               
                                                                             
  219 00000274 00000000        DCD              LPTIM5_IRQHandler ; LP TIM5 glo
                                                            bal interrupt      
                                                                               
                                                                             
  220 00000278 00000000        DCD              LPUART1_IRQHandler ; LP UART1 i
                                                            nterrupt           
                                                                               
                                                                              
  221 0000027C 00000000        DCD              0           ; Reserved         
                                                                               
                                                                               
                                                                               
                                                                        
  222 00000280 00000000        DCD              CRS_IRQHandler ; Clock Recovery
                                                             Global Interrupt  
                                                                               
                                                                          
  223 00000284 00000000        DCD              ECC_IRQHandler ; ECC diagnostic
                                                             Global Interrupt  
                                                                               
                                                                               
                                                                  
  224 00000288 00000000        DCD              SAI4_IRQHandler ; SAI4 global i
                                                            nterrupt           
                                                                               
                                                                              
  225 0000028C 00000000        DCD              DTS_IRQHandler ; DTS  interrupt
                                                                               
                                                                         
  226 00000290 00000000        DCD              0           ; Reserved         
                                                                               
                                                                    
  227 00000294 00000000        DCD              WAKEUP_PIN_IRQHandler ; Interru



ARM Macro Assembler    Page 11 


                                                            pt for all 6 wake-u
                                                            p pins 
  228 00000298 00000000        DCD              OCTOSPI2_IRQHandler ; OCTOSPI2 
                                                            Interrupt
  229 0000029C 00000000        DCD              0           ; Reserved         
                                                                   
  230 000002A0 00000000        DCD              0           ; Reserved
  231 000002A4 00000000        DCD              FMAC_IRQHandler 
                                                            ; FMAC Interrupt
  232 000002A8 00000000        DCD              CORDIC_IRQHandler 
                                                            ; CORDIC Interrupt
  233 000002AC 00000000        DCD              UART9_IRQHandler 
                                                            ; UART9 Interrupt
  234 000002B0 00000000        DCD              USART10_IRQHandler 
                                                            ; UART10 Interrupt
  235 000002B4 00000000        DCD              I2C5_EV_IRQHandler ; I2C5 Event
                                                             Interrupt
  236 000002B8 00000000        DCD              I2C5_ER_IRQHandler ; I2C5 Error
                                                             Interrupt
  237 000002BC 00000000        DCD              FDCAN3_IT0_IRQHandler ; FDCAN3 
                                                            interrupt line 0
  238 000002C0 00000000        DCD              FDCAN3_IT1_IRQHandler ; FDCAN3 
                                                            interrupt line 1
  239 000002C4 00000000        DCD              TIM23_IRQHandler ; TIM23 global
                                                             interrupt
  240 000002C8 00000000        DCD              TIM24_IRQHandler ; TIM24 global
                                                             interrupt
  241 000002CC         
  242 000002CC         __Vectors_End
  243 000002CC         
  244 000002CC 000002CC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  245 000002CC         
  246 000002CC                 AREA             |.text|, CODE, READONLY
  247 00000000         
  248 00000000         ; Reset handler
  249 00000000         Reset_Handler
                               PROC
  250 00000000                 EXPORT           Reset_Handler                  
  [WEAK]
  251 00000000                 IMPORT           ExitRun0Mode
  252 00000000                 IMPORT           SystemInit
  253 00000000                 IMPORT           __main
  254 00000000         
  255 00000000 480A            LDR              R0, =ExitRun0Mode
  256 00000002 4780            BLX              R0
  257 00000004 480A            LDR              R0, =SystemInit
  258 00000006 4780            BLX              R0
  259 00000008 480A            LDR              R0, =__main
  260 0000000A 4700            BX               R0
  261 0000000C                 ENDP
  262 0000000C         
  263 0000000C         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  264 0000000C         
  265 0000000C         NMI_Handler
                               PROC
  266 0000000C                 EXPORT           NMI_Handler                    



ARM Macro Assembler    Page 12 


  [WEAK]
  267 0000000C E7FE            B                .
  268 0000000E                 ENDP
  270 0000000E         HardFault_Handler
                               PROC
  271 0000000E                 EXPORT           HardFault_Handler              
  [WEAK]
  272 0000000E E7FE            B                .
  273 00000010                 ENDP
  275 00000010         MemManage_Handler
                               PROC
  276 00000010                 EXPORT           MemManage_Handler              
  [WEAK]
  277 00000010 E7FE            B                .
  278 00000012                 ENDP
  280 00000012         BusFault_Handler
                               PROC
  281 00000012                 EXPORT           BusFault_Handler               
  [WEAK]
  282 00000012 E7FE            B                .
  283 00000014                 ENDP
  285 00000014         UsageFault_Handler
                               PROC
  286 00000014                 EXPORT           UsageFault_Handler             
  [WEAK]
  287 00000014 E7FE            B                .
  288 00000016                 ENDP
  289 00000016         SVC_Handler
                               PROC
  290 00000016                 EXPORT           SVC_Handler                    
  [WEAK]
  291 00000016 E7FE            B                .
  292 00000018                 ENDP
  294 00000018         DebugMon_Handler
                               PROC
  295 00000018                 EXPORT           DebugMon_Handler               
   [WEAK]
  296 00000018 E7FE            B                .
  297 0000001A                 ENDP
  298 0000001A         PendSV_Handler
                               PROC
  299 0000001A                 EXPORT           PendSV_Handler                 
   [WEAK]
  300 0000001A E7FE            B                .
  301 0000001C                 ENDP
  302 0000001C         SysTick_Handler
                               PROC
  303 0000001C                 EXPORT           SysTick_Handler                
   [WEAK]
  304 0000001C E7FE            B                .
  305 0000001E                 ENDP
  306 0000001E         
  307 0000001E         Default_Handler
                               PROC
  308 0000001E         
  309 0000001E                 EXPORT           WWDG_IRQHandler                
   [WEAK]
  310 0000001E                 EXPORT           PVD_AVD_IRQHandler             
   [WEAK]



ARM Macro Assembler    Page 13 


  311 0000001E                 EXPORT           TAMP_STAMP_IRQHandler          
   [WEAK]
  312 0000001E                 EXPORT           RTC_WKUP_IRQHandler            
   [WEAK]
  313 0000001E                 EXPORT           FLASH_IRQHandler               
   [WEAK]
  314 0000001E                 EXPORT           RCC_IRQHandler                 
   [WEAK]
  315 0000001E                 EXPORT           EXTI0_IRQHandler               
   [WEAK]
  316 0000001E                 EXPORT           EXTI1_IRQHandler               
   [WEAK]
  317 0000001E                 EXPORT           EXTI2_IRQHandler               
   [WEAK]
  318 0000001E                 EXPORT           EXTI3_IRQHandler               
   [WEAK]
  319 0000001E                 EXPORT           EXTI4_IRQHandler               
   [WEAK]
  320 0000001E                 EXPORT           DMA1_Stream0_IRQHandler        
   [WEAK]
  321 0000001E                 EXPORT           DMA1_Stream1_IRQHandler        
   [WEAK]
  322 0000001E                 EXPORT           DMA1_Stream2_IRQHandler        
   [WEAK]
  323 0000001E                 EXPORT           DMA1_Stream3_IRQHandler        
   [WEAK]
  324 0000001E                 EXPORT           DMA1_Stream4_IRQHandler        
   [WEAK]
  325 0000001E                 EXPORT           DMA1_Stream5_IRQHandler        
   [WEAK]
  326 0000001E                 EXPORT           DMA1_Stream6_IRQHandler        
   [WEAK]
  327 0000001E                 EXPORT           DMA1_Stream7_IRQHandler        
   [WEAK]
  328 0000001E                 EXPORT           ADC_IRQHandler                 
   [WEAK]
  329 0000001E                 EXPORT           FDCAN1_IT0_IRQHandler          
   [WEAK]
  330 0000001E                 EXPORT           FDCAN2_IT0_IRQHandler          
   [WEAK]
  331 0000001E                 EXPORT           FDCAN1_IT1_IRQHandler          
   [WEAK]
  332 0000001E                 EXPORT           FDCAN2_IT1_IRQHandler          
   [WEAK]
  333 0000001E                 EXPORT           EXTI9_5_IRQHandler             
   [WEAK]
  334 0000001E                 EXPORT           TIM1_BRK_IRQHandler            
   [WEAK]
  335 0000001E                 EXPORT           TIM1_UP_IRQHandler             
   [WEAK]
  336 0000001E                 EXPORT           TIM1_TRG_COM_IRQHandler        
   [WEAK]
  337 0000001E                 EXPORT           TIM1_CC_IRQHandler             
   [WEAK]
  338 0000001E                 EXPORT           TIM2_IRQHandler                
   [WEAK]
  339 0000001E                 EXPORT           TIM3_IRQHandler                
   [WEAK]
  340 0000001E                 EXPORT           TIM4_IRQHandler                



ARM Macro Assembler    Page 14 


   [WEAK]
  341 0000001E                 EXPORT           I2C1_EV_IRQHandler             
   [WEAK]
  342 0000001E                 EXPORT           I2C1_ER_IRQHandler             
   [WEAK]
  343 0000001E                 EXPORT           I2C2_EV_IRQHandler             
   [WEAK]
  344 0000001E                 EXPORT           I2C2_ER_IRQHandler             
   [WEAK]
  345 0000001E                 EXPORT           SPI1_IRQHandler                
   [WEAK]
  346 0000001E                 EXPORT           SPI2_IRQHandler                
   [WEAK]
  347 0000001E                 EXPORT           USART1_IRQHandler              
   [WEAK]
  348 0000001E                 EXPORT           USART2_IRQHandler              
   [WEAK]
  349 0000001E                 EXPORT           USART3_IRQHandler              
   [WEAK]
  350 0000001E                 EXPORT           EXTI15_10_IRQHandler           
   [WEAK]
  351 0000001E                 EXPORT           RTC_Alarm_IRQHandler           
   [WEAK]
  352 0000001E                 EXPORT           TIM8_BRK_TIM12_IRQHandler      
   [WEAK]
  353 0000001E                 EXPORT           TIM8_UP_TIM13_IRQHandler       
   [WEAK]
  354 0000001E                 EXPORT           TIM8_TRG_COM_TIM14_IRQHandler  
   [WEAK]
  355 0000001E                 EXPORT           TIM8_CC_IRQHandler             
   [WEAK]
  356 0000001E                 EXPORT           DMA1_Stream7_IRQHandler        
   [WEAK]
  357 0000001E                 EXPORT           FMC_IRQHandler                 
   [WEAK]
  358 0000001E                 EXPORT           SDMMC1_IRQHandler              
   [WEAK]
  359 0000001E                 EXPORT           TIM5_IRQHandler                
   [WEAK]
  360 0000001E                 EXPORT           SPI3_IRQHandler                
   [WEAK]
  361 0000001E                 EXPORT           UART4_IRQHandler               
   [WEAK]
  362 0000001E                 EXPORT           UART5_IRQHandler               
   [WEAK]
  363 0000001E                 EXPORT           TIM6_DAC_IRQHandler            
   [WEAK]
  364 0000001E                 EXPORT           TIM7_IRQHandler                
   [WEAK]
  365 0000001E                 EXPORT           DMA2_Stream0_IRQHandler        
   [WEAK]
  366 0000001E                 EXPORT           DMA2_Stream1_IRQHandler        
   [WEAK]
  367 0000001E                 EXPORT           DMA2_Stream2_IRQHandler        
   [WEAK]
  368 0000001E                 EXPORT           DMA2_Stream3_IRQHandler        
   [WEAK]
  369 0000001E                 EXPORT           DMA2_Stream4_IRQHandler        
   [WEAK]



ARM Macro Assembler    Page 15 


  370 0000001E                 EXPORT           ETH_IRQHandler                 
   [WEAK]
  371 0000001E                 EXPORT           ETH_WKUP_IRQHandler            
   [WEAK]
  372 0000001E                 EXPORT           FDCAN_CAL_IRQHandler           
   [WEAK]
  373 0000001E                 EXPORT           DMA2_Stream5_IRQHandler        
   [WEAK]
  374 0000001E                 EXPORT           DMA2_Stream6_IRQHandler        
   [WEAK]
  375 0000001E                 EXPORT           DMA2_Stream7_IRQHandler        
   [WEAK]
  376 0000001E                 EXPORT           USART6_IRQHandler              
   [WEAK]
  377 0000001E                 EXPORT           I2C3_EV_IRQHandler             
   [WEAK]
  378 0000001E                 EXPORT           I2C3_ER_IRQHandler             
   [WEAK]
  379 0000001E                 EXPORT           OTG_HS_EP1_OUT_IRQHandler      
   [WEAK]
  380 0000001E                 EXPORT           OTG_HS_EP1_IN_IRQHandler       
   [WEAK]
  381 0000001E                 EXPORT           OTG_HS_WKUP_IRQHandler         
   [WEAK]
  382 0000001E                 EXPORT           OTG_HS_IRQHandler              
   [WEAK]
  383 0000001E                 EXPORT           DCMI_PSSI_IRQHandler           
   [WEAK]
  384 0000001E                 EXPORT           RNG_IRQHandler                 
   [WEAK]
  385 0000001E                 EXPORT           FPU_IRQHandler                 
   [WEAK]
  386 0000001E                 EXPORT           UART7_IRQHandler               
   [WEAK]
  387 0000001E                 EXPORT           UART8_IRQHandler               
   [WEAK]
  388 0000001E                 EXPORT           SPI4_IRQHandler                
   [WEAK]
  389 0000001E                 EXPORT           SPI5_IRQHandler                
   [WEAK]
  390 0000001E                 EXPORT           SPI6_IRQHandler                
   [WEAK]
  391 0000001E                 EXPORT           SAI1_IRQHandler                
   [WEAK]
  392 0000001E                 EXPORT           LTDC_IRQHandler                
   [WEAK]
  393 0000001E                 EXPORT           LTDC_ER_IRQHandler             
   [WEAK]
  394 0000001E                 EXPORT           DMA2D_IRQHandler               
   [WEAK]
  395 0000001E                 EXPORT           OCTOSPI1_IRQHandler            
   [WEAK]
  396 0000001E                 EXPORT           LPTIM1_IRQHandler              
   [WEAK]
  397 0000001E                 EXPORT           CEC_IRQHandler                 
   [WEAK]
  398 0000001E                 EXPORT           I2C4_EV_IRQHandler             
   [WEAK]
  399 0000001E                 EXPORT           I2C4_ER_IRQHandler             



ARM Macro Assembler    Page 16 


   [WEAK]
  400 0000001E                 EXPORT           SPDIF_RX_IRQHandler            
   [WEAK]
  401 0000001E                 EXPORT           DMAMUX1_OVR_IRQHandler         
   [WEAK]
  402 0000001E                 EXPORT           DFSDM1_FLT0_IRQHandler         
   [WEAK]
  403 0000001E                 EXPORT           DFSDM1_FLT1_IRQHandler         
   [WEAK]
  404 0000001E                 EXPORT           DFSDM1_FLT2_IRQHandler         
   [WEAK]
  405 0000001E                 EXPORT           DFSDM1_FLT3_IRQHandler         
   [WEAK]
  406 0000001E                 EXPORT           SWPMI1_IRQHandler              
   [WEAK]
  407 0000001E                 EXPORT           TIM15_IRQHandler               
   [WEAK]
  408 0000001E                 EXPORT           TIM16_IRQHandler               
   [WEAK]
  409 0000001E                 EXPORT           TIM17_IRQHandler               
   [WEAK]
  410 0000001E                 EXPORT           MDIOS_WKUP_IRQHandler          
   [WEAK]
  411 0000001E                 EXPORT           MDIOS_IRQHandler               
   [WEAK]
  412 0000001E                 EXPORT           MDMA_IRQHandler                
   [WEAK]
  413 0000001E                 EXPORT           SDMMC2_IRQHandler              
   [WEAK]
  414 0000001E                 EXPORT           HSEM1_IRQHandler               
   [WEAK]
  415 0000001E                 EXPORT           ADC3_IRQHandler                
   [WEAK]
  416 0000001E                 EXPORT           DMAMUX2_OVR_IRQHandler         
   [WEAK]
  417 0000001E                 EXPORT           BDMA_Channel0_IRQHandler       
   [WEAK]
  418 0000001E                 EXPORT           BDMA_Channel1_IRQHandler       
   [WEAK]
  419 0000001E                 EXPORT           BDMA_Channel2_IRQHandler       
   [WEAK]
  420 0000001E                 EXPORT           BDMA_Channel3_IRQHandler       
   [WEAK]
  421 0000001E                 EXPORT           BDMA_Channel4_IRQHandler       
   [WEAK]
  422 0000001E                 EXPORT           BDMA_Channel5_IRQHandler       
   [WEAK]
  423 0000001E                 EXPORT           BDMA_Channel6_IRQHandler       
   [WEAK]
  424 0000001E                 EXPORT           BDMA_Channel7_IRQHandler       
   [WEAK]
  425 0000001E                 EXPORT           COMP1_IRQHandler               
   [WEAK]
  426 0000001E                 EXPORT           LPTIM2_IRQHandler              
   [WEAK]
  427 0000001E                 EXPORT           LPTIM3_IRQHandler              
   [WEAK]
  428 0000001E                 EXPORT           LPTIM4_IRQHandler              
   [WEAK]



ARM Macro Assembler    Page 17 


  429 0000001E                 EXPORT           LPTIM5_IRQHandler              
   [WEAK]
  430 0000001E                 EXPORT           LPUART1_IRQHandler             
   [WEAK]
  431 0000001E                 EXPORT           CRS_IRQHandler                 
   [WEAK]
  432 0000001E                 EXPORT           ECC_IRQHandler                 
   [WEAK]
  433 0000001E                 EXPORT           SAI4_IRQHandler                
   [WEAK]
  434 0000001E                 EXPORT           DTS_IRQHandler                 
   [WEAK]
  435 0000001E                 EXPORT           WAKEUP_PIN_IRQHandler          
   [WEAK]
  436 0000001E                 EXPORT           OCTOSPI2_IRQHandler            
   [WEAK]
  437 0000001E                 EXPORT           FMAC_IRQHandler                
   [WEAK]
  438 0000001E                 EXPORT           CORDIC_IRQHandler              
   [WEAK]
  439 0000001E                 EXPORT           UART9_IRQHandler               
   [WEAK]
  440 0000001E                 EXPORT           USART10_IRQHandler             
   [WEAK]
  441 0000001E                 EXPORT           I2C5_EV_IRQHandler             
   [WEAK]
  442 0000001E                 EXPORT           I2C5_ER_IRQHandler             
   [WEAK]
  443 0000001E                 EXPORT           FDCAN3_IT0_IRQHandler          
   [WEAK]
  444 0000001E                 EXPORT           FDCAN3_IT1_IRQHandler          
   [WEAK]
  445 0000001E                 EXPORT           TIM23_IRQHandler               
   [WEAK]
  446 0000001E                 EXPORT           TIM24_IRQHandler               
   [WEAK]
  447 0000001E         
  448 0000001E         WWDG_IRQHandler
  449 0000001E         PVD_AVD_IRQHandler
  450 0000001E         TAMP_STAMP_IRQHandler
  451 0000001E         RTC_WKUP_IRQHandler
  452 0000001E         FLASH_IRQHandler
  453 0000001E         RCC_IRQHandler
  454 0000001E         EXTI0_IRQHandler
  455 0000001E         EXTI1_IRQHandler
  456 0000001E         EXTI2_IRQHandler
  457 0000001E         EXTI3_IRQHandler
  458 0000001E         EXTI4_IRQHandler
  459 0000001E         DMA1_Stream0_IRQHandler
  460 0000001E         DMA1_Stream1_IRQHandler
  461 0000001E         DMA1_Stream2_IRQHandler
  462 0000001E         DMA1_Stream3_IRQHandler
  463 0000001E         DMA1_Stream4_IRQHandler
  464 0000001E         DMA1_Stream5_IRQHandler
  465 0000001E         DMA1_Stream6_IRQHandler
  466 0000001E         ADC_IRQHandler
  467 0000001E         FDCAN1_IT0_IRQHandler
  468 0000001E         FDCAN2_IT0_IRQHandler
  469 0000001E         FDCAN1_IT1_IRQHandler



ARM Macro Assembler    Page 18 


  470 0000001E         FDCAN2_IT1_IRQHandler
  471 0000001E         EXTI9_5_IRQHandler
  472 0000001E         TIM1_BRK_IRQHandler
  473 0000001E         TIM1_UP_IRQHandler
  474 0000001E         TIM1_TRG_COM_IRQHandler
  475 0000001E         TIM1_CC_IRQHandler
  476 0000001E         TIM2_IRQHandler
  477 0000001E         TIM3_IRQHandler
  478 0000001E         TIM4_IRQHandler
  479 0000001E         I2C1_EV_IRQHandler
  480 0000001E         I2C1_ER_IRQHandler
  481 0000001E         I2C2_EV_IRQHandler
  482 0000001E         I2C2_ER_IRQHandler
  483 0000001E         SPI1_IRQHandler
  484 0000001E         SPI2_IRQHandler
  485 0000001E         USART1_IRQHandler
  486 0000001E         USART2_IRQHandler
  487 0000001E         USART3_IRQHandler
  488 0000001E         EXTI15_10_IRQHandler
  489 0000001E         RTC_Alarm_IRQHandler
  490 0000001E         TIM8_BRK_TIM12_IRQHandler
  491 0000001E         TIM8_UP_TIM13_IRQHandler
  492 0000001E         TIM8_TRG_COM_TIM14_IRQHandler
  493 0000001E         TIM8_CC_IRQHandler
  494 0000001E         DMA1_Stream7_IRQHandler
  495 0000001E         FMC_IRQHandler
  496 0000001E         SDMMC1_IRQHandler
  497 0000001E         TIM5_IRQHandler
  498 0000001E         SPI3_IRQHandler
  499 0000001E         UART4_IRQHandler
  500 0000001E         UART5_IRQHandler
  501 0000001E         TIM6_DAC_IRQHandler
  502 0000001E         TIM7_IRQHandler
  503 0000001E         DMA2_Stream0_IRQHandler
  504 0000001E         DMA2_Stream1_IRQHandler
  505 0000001E         DMA2_Stream2_IRQHandler
  506 0000001E         DMA2_Stream3_IRQHandler
  507 0000001E         DMA2_Stream4_IRQHandler
  508 0000001E         ETH_IRQHandler
  509 0000001E         ETH_WKUP_IRQHandler
  510 0000001E         FDCAN_CAL_IRQHandler
  511 0000001E         DMA2_Stream5_IRQHandler
  512 0000001E         DMA2_Stream6_IRQHandler
  513 0000001E         DMA2_Stream7_IRQHandler
  514 0000001E         USART6_IRQHandler
  515 0000001E         I2C3_EV_IRQHandler
  516 0000001E         I2C3_ER_IRQHandler
  517 0000001E         OTG_HS_EP1_OUT_IRQHandler
  518 0000001E         OTG_HS_EP1_IN_IRQHandler
  519 0000001E         OTG_HS_WKUP_IRQHandler
  520 0000001E         OTG_HS_IRQHandler
  521 0000001E         DCMI_PSSI_IRQHandler
  522 0000001E         RNG_IRQHandler
  523 0000001E         FPU_IRQHandler
  524 0000001E         UART7_IRQHandler
  525 0000001E         UART8_IRQHandler
  526 0000001E         SPI4_IRQHandler
  527 0000001E         SPI5_IRQHandler
  528 0000001E         SPI6_IRQHandler



ARM Macro Assembler    Page 19 


  529 0000001E         SAI1_IRQHandler
  530 0000001E         LTDC_IRQHandler
  531 0000001E         LTDC_ER_IRQHandler
  532 0000001E         DMA2D_IRQHandler
  533 0000001E         OCTOSPI1_IRQHandler
  534 0000001E         LPTIM1_IRQHandler
  535 0000001E         CEC_IRQHandler
  536 0000001E         I2C4_EV_IRQHandler
  537 0000001E         I2C4_ER_IRQHandler
  538 0000001E         SPDIF_RX_IRQHandler
  539 0000001E         DMAMUX1_OVR_IRQHandler
  540 0000001E         DFSDM1_FLT0_IRQHandler
  541 0000001E         DFSDM1_FLT1_IRQHandler
  542 0000001E         DFSDM1_FLT2_IRQHandler
  543 0000001E         DFSDM1_FLT3_IRQHandler
  544 0000001E         SWPMI1_IRQHandler
  545 0000001E         TIM15_IRQHandler
  546 0000001E         TIM16_IRQHandler
  547 0000001E         TIM17_IRQHandler
  548 0000001E         MDIOS_WKUP_IRQHandler
  549 0000001E         MDIOS_IRQHandler
  550 0000001E         MDMA_IRQHandler
  551 0000001E         SDMMC2_IRQHandler
  552 0000001E         HSEM1_IRQHandler
  553 0000001E         ADC3_IRQHandler
  554 0000001E         DMAMUX2_OVR_IRQHandler
  555 0000001E         BDMA_Channel0_IRQHandler
  556 0000001E         BDMA_Channel1_IRQHandler
  557 0000001E         BDMA_Channel2_IRQHandler
  558 0000001E         BDMA_Channel3_IRQHandler
  559 0000001E         BDMA_Channel4_IRQHandler
  560 0000001E         BDMA_Channel5_IRQHandler
  561 0000001E         BDMA_Channel6_IRQHandler
  562 0000001E         BDMA_Channel7_IRQHandler
  563 0000001E         COMP1_IRQHandler
  564 0000001E         LPTIM2_IRQHandler
  565 0000001E         LPTIM3_IRQHandler
  566 0000001E         LPTIM4_IRQHandler
  567 0000001E         LPTIM5_IRQHandler
  568 0000001E         LPUART1_IRQHandler
  569 0000001E         CRS_IRQHandler
  570 0000001E         ECC_IRQHandler
  571 0000001E         SAI4_IRQHandler
  572 0000001E         DTS_IRQHandler
  573 0000001E         WAKEUP_PIN_IRQHandler
  574 0000001E         OCTOSPI2_IRQHandler
  575 0000001E         FMAC_IRQHandler
  576 0000001E         CORDIC_IRQHandler
  577 0000001E         UART9_IRQHandler
  578 0000001E         USART10_IRQHandler
  579 0000001E         I2C5_EV_IRQHandler
  580 0000001E         I2C5_ER_IRQHandler
  581 0000001E         FDCAN3_IT0_IRQHandler
  582 0000001E         FDCAN3_IT1_IRQHandler
  583 0000001E         TIM23_IRQHandler
  584 0000001E         TIM24_IRQHandler
  585 0000001E         
  586 0000001E E7FE            B                .
  587 00000020         



ARM Macro Assembler    Page 20 


  588 00000020                 ENDP
  589 00000020         
  590 00000020                 ALIGN
  591 00000020         
  592 00000020         ;*******************************************************
                       ************************
  593 00000020         ; User Stack and Heap initialization
  594 00000020         ;*******************************************************
                       ************************
  595 00000020                 IF               :DEF:__MICROLIB
  602 00000020         
  603 00000020                 IMPORT           __use_two_region_memory
  604 00000020                 EXPORT           __user_initial_stackheap
  605 00000020         
  606 00000020         __user_initial_stackheap
  607 00000020         
  608 00000020 4805            LDR              R0, =  Heap_Mem
  609 00000022 4906            LDR              R1, =(Stack_Mem + Stack_Size)
  610 00000024 4A06            LDR              R2, = (Heap_Mem +  Heap_Size)
  611 00000026 4B07            LDR              R3, = Stack_Mem
  612 00000028 4770            BX               LR
  613 0000002A         
  614 0000002A 00 00           ALIGN
  615 0000002C         
  616 0000002C                 ENDIF
  617 0000002C         
  618 0000002C                 END
              00000000 
              00000000 
              00000000 
              00000000 
              00000400 
              00000200 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M7.fp.dp --apcs=
interwork --depend="defect detection cart\startup_stm32h723xx.d" -o"defect dete
ction cart\startup_stm32h723xx.o" -I.\RTE\_Defect_detection_cart -IE:\Keil_v5\P
acks\ARM\CMSIS\5.7.0\CMSIS\Core\Include -IE:\Keil_v5\Packs\Keil\STM32H7xx_DFP\2
.7.0\Drivers\CMSIS\Device\ST\STM32H7xx\Include --predefine="__UVISION_VERSION S
ETA 534" --predefine="_RTE_ SETA 1" --predefine="STM32H723xx SETA 1" --predefin
e="_RTE_ SETA 1" --list=startup_stm32h723xx.lst startup_stm32h723xx.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 34 in file startup_stm32h723xx.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 35 in file startup_stm32h723xx.s
   Uses
      At line 609 in file startup_stm32h723xx.s
      At line 611 in file startup_stm32h723xx.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 36 in file startup_stm32h723xx.s
   Uses
      At line 60 in file startup_stm32h723xx.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 45 in file startup_stm32h723xx.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 47 in file startup_stm32h723xx.s
   Uses
      At line 608 in file startup_stm32h723xx.s
      At line 610 in file startup_stm32h723xx.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 46 in file startup_stm32h723xx.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 48 in file startup_stm32h723xx.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 55 in file startup_stm32h723xx.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 60 in file startup_stm32h723xx.s
   Uses
      At line 56 in file startup_stm32h723xx.s
      At line 244 in file startup_stm32h723xx.s

__Vectors_End 000002CC

Symbol: __Vectors_End
   Definitions
      At line 242 in file startup_stm32h723xx.s
   Uses
      At line 57 in file startup_stm32h723xx.s
      At line 244 in file startup_stm32h723xx.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 246 in file startup_stm32h723xx.s
   Uses
      None
Comment: .text unused
ADC3_IRQHandler 0000001E

Symbol: ADC3_IRQHandler
   Definitions
      At line 553 in file startup_stm32h723xx.s
   Uses
      At line 205 in file startup_stm32h723xx.s
      At line 415 in file startup_stm32h723xx.s

ADC_IRQHandler 0000001E

Symbol: ADC_IRQHandler
   Definitions
      At line 466 in file startup_stm32h723xx.s
   Uses
      At line 96 in file startup_stm32h723xx.s
      At line 328 in file startup_stm32h723xx.s

BDMA_Channel0_IRQHandler 0000001E

Symbol: BDMA_Channel0_IRQHandler
   Definitions
      At line 555 in file startup_stm32h723xx.s
   Uses
      At line 207 in file startup_stm32h723xx.s
      At line 417 in file startup_stm32h723xx.s

BDMA_Channel1_IRQHandler 0000001E

Symbol: BDMA_Channel1_IRQHandler
   Definitions
      At line 556 in file startup_stm32h723xx.s
   Uses
      At line 208 in file startup_stm32h723xx.s
      At line 418 in file startup_stm32h723xx.s

BDMA_Channel2_IRQHandler 0000001E

Symbol: BDMA_Channel2_IRQHandler
   Definitions
      At line 557 in file startup_stm32h723xx.s
   Uses
      At line 209 in file startup_stm32h723xx.s
      At line 419 in file startup_stm32h723xx.s

BDMA_Channel3_IRQHandler 0000001E

Symbol: BDMA_Channel3_IRQHandler
   Definitions
      At line 558 in file startup_stm32h723xx.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 210 in file startup_stm32h723xx.s
      At line 420 in file startup_stm32h723xx.s

BDMA_Channel4_IRQHandler 0000001E

Symbol: BDMA_Channel4_IRQHandler
   Definitions
      At line 559 in file startup_stm32h723xx.s
   Uses
      At line 211 in file startup_stm32h723xx.s
      At line 421 in file startup_stm32h723xx.s

BDMA_Channel5_IRQHandler 0000001E

Symbol: BDMA_Channel5_IRQHandler
   Definitions
      At line 560 in file startup_stm32h723xx.s
   Uses
      At line 212 in file startup_stm32h723xx.s
      At line 422 in file startup_stm32h723xx.s

BDMA_Channel6_IRQHandler 0000001E

Symbol: BDMA_Channel6_IRQHandler
   Definitions
      At line 561 in file startup_stm32h723xx.s
   Uses
      At line 213 in file startup_stm32h723xx.s
      At line 423 in file startup_stm32h723xx.s

BDMA_Channel7_IRQHandler 0000001E

Symbol: BDMA_Channel7_IRQHandler
   Definitions
      At line 562 in file startup_stm32h723xx.s
   Uses
      At line 214 in file startup_stm32h723xx.s
      At line 424 in file startup_stm32h723xx.s

BusFault_Handler 00000012

Symbol: BusFault_Handler
   Definitions
      At line 280 in file startup_stm32h723xx.s
   Uses
      At line 65 in file startup_stm32h723xx.s
      At line 281 in file startup_stm32h723xx.s

CEC_IRQHandler 0000001E

Symbol: CEC_IRQHandler
   Definitions
      At line 535 in file startup_stm32h723xx.s
   Uses
      At line 172 in file startup_stm32h723xx.s
      At line 397 in file startup_stm32h723xx.s

COMP1_IRQHandler 0000001E




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: COMP1_IRQHandler
   Definitions
      At line 563 in file startup_stm32h723xx.s
   Uses
      At line 215 in file startup_stm32h723xx.s
      At line 425 in file startup_stm32h723xx.s

CORDIC_IRQHandler 0000001E

Symbol: CORDIC_IRQHandler
   Definitions
      At line 576 in file startup_stm32h723xx.s
   Uses
      At line 232 in file startup_stm32h723xx.s
      At line 438 in file startup_stm32h723xx.s

CRS_IRQHandler 0000001E

Symbol: CRS_IRQHandler
   Definitions
      At line 569 in file startup_stm32h723xx.s
   Uses
      At line 222 in file startup_stm32h723xx.s
      At line 431 in file startup_stm32h723xx.s

DCMI_PSSI_IRQHandler 0000001E

Symbol: DCMI_PSSI_IRQHandler
   Definitions
      At line 521 in file startup_stm32h723xx.s
   Uses
      At line 156 in file startup_stm32h723xx.s
      At line 383 in file startup_stm32h723xx.s

DFSDM1_FLT0_IRQHandler 0000001E

Symbol: DFSDM1_FLT0_IRQHandler
   Definitions
      At line 540 in file startup_stm32h723xx.s
   Uses
      At line 188 in file startup_stm32h723xx.s
      At line 402 in file startup_stm32h723xx.s

DFSDM1_FLT1_IRQHandler 0000001E

Symbol: DFSDM1_FLT1_IRQHandler
   Definitions
      At line 541 in file startup_stm32h723xx.s
   Uses
      At line 189 in file startup_stm32h723xx.s
      At line 403 in file startup_stm32h723xx.s

DFSDM1_FLT2_IRQHandler 0000001E

Symbol: DFSDM1_FLT2_IRQHandler
   Definitions
      At line 542 in file startup_stm32h723xx.s
   Uses
      At line 190 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols

      At line 404 in file startup_stm32h723xx.s

DFSDM1_FLT3_IRQHandler 0000001E

Symbol: DFSDM1_FLT3_IRQHandler
   Definitions
      At line 543 in file startup_stm32h723xx.s
   Uses
      At line 191 in file startup_stm32h723xx.s
      At line 405 in file startup_stm32h723xx.s

DMA1_Stream0_IRQHandler 0000001E

Symbol: DMA1_Stream0_IRQHandler
   Definitions
      At line 459 in file startup_stm32h723xx.s
   Uses
      At line 89 in file startup_stm32h723xx.s
      At line 320 in file startup_stm32h723xx.s

DMA1_Stream1_IRQHandler 0000001E

Symbol: DMA1_Stream1_IRQHandler
   Definitions
      At line 460 in file startup_stm32h723xx.s
   Uses
      At line 90 in file startup_stm32h723xx.s
      At line 321 in file startup_stm32h723xx.s

DMA1_Stream2_IRQHandler 0000001E

Symbol: DMA1_Stream2_IRQHandler
   Definitions
      At line 461 in file startup_stm32h723xx.s
   Uses
      At line 91 in file startup_stm32h723xx.s
      At line 322 in file startup_stm32h723xx.s

DMA1_Stream3_IRQHandler 0000001E

Symbol: DMA1_Stream3_IRQHandler
   Definitions
      At line 462 in file startup_stm32h723xx.s
   Uses
      At line 92 in file startup_stm32h723xx.s
      At line 323 in file startup_stm32h723xx.s

DMA1_Stream4_IRQHandler 0000001E

Symbol: DMA1_Stream4_IRQHandler
   Definitions
      At line 463 in file startup_stm32h723xx.s
   Uses
      At line 93 in file startup_stm32h723xx.s
      At line 324 in file startup_stm32h723xx.s

DMA1_Stream5_IRQHandler 0000001E

Symbol: DMA1_Stream5_IRQHandler



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 464 in file startup_stm32h723xx.s
   Uses
      At line 94 in file startup_stm32h723xx.s
      At line 325 in file startup_stm32h723xx.s

DMA1_Stream6_IRQHandler 0000001E

Symbol: DMA1_Stream6_IRQHandler
   Definitions
      At line 465 in file startup_stm32h723xx.s
   Uses
      At line 95 in file startup_stm32h723xx.s
      At line 326 in file startup_stm32h723xx.s

DMA1_Stream7_IRQHandler 0000001E

Symbol: DMA1_Stream7_IRQHandler
   Definitions
      At line 494 in file startup_stm32h723xx.s
   Uses
      At line 125 in file startup_stm32h723xx.s
      At line 327 in file startup_stm32h723xx.s
      At line 356 in file startup_stm32h723xx.s

DMA2D_IRQHandler 0000001E

Symbol: DMA2D_IRQHandler
   Definitions
      At line 532 in file startup_stm32h723xx.s
   Uses
      At line 168 in file startup_stm32h723xx.s
      At line 394 in file startup_stm32h723xx.s

DMA2_Stream0_IRQHandler 0000001E

Symbol: DMA2_Stream0_IRQHandler
   Definitions
      At line 503 in file startup_stm32h723xx.s
   Uses
      At line 134 in file startup_stm32h723xx.s
      At line 365 in file startup_stm32h723xx.s

DMA2_Stream1_IRQHandler 0000001E

Symbol: DMA2_Stream1_IRQHandler
   Definitions
      At line 504 in file startup_stm32h723xx.s
   Uses
      At line 135 in file startup_stm32h723xx.s
      At line 366 in file startup_stm32h723xx.s

DMA2_Stream2_IRQHandler 0000001E

Symbol: DMA2_Stream2_IRQHandler
   Definitions
      At line 505 in file startup_stm32h723xx.s
   Uses
      At line 136 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

      At line 367 in file startup_stm32h723xx.s

DMA2_Stream3_IRQHandler 0000001E

Symbol: DMA2_Stream3_IRQHandler
   Definitions
      At line 506 in file startup_stm32h723xx.s
   Uses
      At line 137 in file startup_stm32h723xx.s
      At line 368 in file startup_stm32h723xx.s

DMA2_Stream4_IRQHandler 0000001E

Symbol: DMA2_Stream4_IRQHandler
   Definitions
      At line 507 in file startup_stm32h723xx.s
   Uses
      At line 138 in file startup_stm32h723xx.s
      At line 369 in file startup_stm32h723xx.s

DMA2_Stream5_IRQHandler 0000001E

Symbol: DMA2_Stream5_IRQHandler
   Definitions
      At line 511 in file startup_stm32h723xx.s
   Uses
      At line 146 in file startup_stm32h723xx.s
      At line 373 in file startup_stm32h723xx.s

DMA2_Stream6_IRQHandler 0000001E

Symbol: DMA2_Stream6_IRQHandler
   Definitions
      At line 512 in file startup_stm32h723xx.s
   Uses
      At line 147 in file startup_stm32h723xx.s
      At line 374 in file startup_stm32h723xx.s

DMA2_Stream7_IRQHandler 0000001E

Symbol: DMA2_Stream7_IRQHandler
   Definitions
      At line 513 in file startup_stm32h723xx.s
   Uses
      At line 148 in file startup_stm32h723xx.s
      At line 375 in file startup_stm32h723xx.s

DMAMUX1_OVR_IRQHandler 0000001E

Symbol: DMAMUX1_OVR_IRQHandler
   Definitions
      At line 539 in file startup_stm32h723xx.s
   Uses
      At line 180 in file startup_stm32h723xx.s
      At line 401 in file startup_stm32h723xx.s

DMAMUX2_OVR_IRQHandler 0000001E

Symbol: DMAMUX2_OVR_IRQHandler



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 554 in file startup_stm32h723xx.s
   Uses
      At line 206 in file startup_stm32h723xx.s
      At line 416 in file startup_stm32h723xx.s

DTS_IRQHandler 0000001E

Symbol: DTS_IRQHandler
   Definitions
      At line 572 in file startup_stm32h723xx.s
   Uses
      At line 225 in file startup_stm32h723xx.s
      At line 434 in file startup_stm32h723xx.s

DebugMon_Handler 00000018

Symbol: DebugMon_Handler
   Definitions
      At line 294 in file startup_stm32h723xx.s
   Uses
      At line 72 in file startup_stm32h723xx.s
      At line 295 in file startup_stm32h723xx.s

Default_Handler 0000001E

Symbol: Default_Handler
   Definitions
      At line 307 in file startup_stm32h723xx.s
   Uses
      None
Comment: Default_Handler unused
ECC_IRQHandler 0000001E

Symbol: ECC_IRQHandler
   Definitions
      At line 570 in file startup_stm32h723xx.s
   Uses
      At line 223 in file startup_stm32h723xx.s
      At line 432 in file startup_stm32h723xx.s

ETH_IRQHandler 0000001E

Symbol: ETH_IRQHandler
   Definitions
      At line 508 in file startup_stm32h723xx.s
   Uses
      At line 139 in file startup_stm32h723xx.s
      At line 370 in file startup_stm32h723xx.s

ETH_WKUP_IRQHandler 0000001E

Symbol: ETH_WKUP_IRQHandler
   Definitions
      At line 509 in file startup_stm32h723xx.s
   Uses
      At line 140 in file startup_stm32h723xx.s
      At line 371 in file startup_stm32h723xx.s




ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols

EXTI0_IRQHandler 0000001E

Symbol: EXTI0_IRQHandler
   Definitions
      At line 454 in file startup_stm32h723xx.s
   Uses
      At line 84 in file startup_stm32h723xx.s
      At line 315 in file startup_stm32h723xx.s

EXTI15_10_IRQHandler 0000001E

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 488 in file startup_stm32h723xx.s
   Uses
      At line 118 in file startup_stm32h723xx.s
      At line 350 in file startup_stm32h723xx.s

EXTI1_IRQHandler 0000001E

Symbol: EXTI1_IRQHandler
   Definitions
      At line 455 in file startup_stm32h723xx.s
   Uses
      At line 85 in file startup_stm32h723xx.s
      At line 316 in file startup_stm32h723xx.s

EXTI2_IRQHandler 0000001E

Symbol: EXTI2_IRQHandler
   Definitions
      At line 456 in file startup_stm32h723xx.s
   Uses
      At line 86 in file startup_stm32h723xx.s
      At line 317 in file startup_stm32h723xx.s

EXTI3_IRQHandler 0000001E

Symbol: EXTI3_IRQHandler
   Definitions
      At line 457 in file startup_stm32h723xx.s
   Uses
      At line 87 in file startup_stm32h723xx.s
      At line 318 in file startup_stm32h723xx.s

EXTI4_IRQHandler 0000001E

Symbol: EXTI4_IRQHandler
   Definitions
      At line 458 in file startup_stm32h723xx.s
   Uses
      At line 88 in file startup_stm32h723xx.s
      At line 319 in file startup_stm32h723xx.s

EXTI9_5_IRQHandler 0000001E

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 471 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 101 in file startup_stm32h723xx.s
      At line 333 in file startup_stm32h723xx.s

FDCAN1_IT0_IRQHandler 0000001E

Symbol: FDCAN1_IT0_IRQHandler
   Definitions
      At line 467 in file startup_stm32h723xx.s
   Uses
      At line 97 in file startup_stm32h723xx.s
      At line 329 in file startup_stm32h723xx.s

FDCAN1_IT1_IRQHandler 0000001E

Symbol: FDCAN1_IT1_IRQHandler
   Definitions
      At line 469 in file startup_stm32h723xx.s
   Uses
      At line 99 in file startup_stm32h723xx.s
      At line 331 in file startup_stm32h723xx.s

FDCAN2_IT0_IRQHandler 0000001E

Symbol: FDCAN2_IT0_IRQHandler
   Definitions
      At line 468 in file startup_stm32h723xx.s
   Uses
      At line 98 in file startup_stm32h723xx.s
      At line 330 in file startup_stm32h723xx.s

FDCAN2_IT1_IRQHandler 0000001E

Symbol: FDCAN2_IT1_IRQHandler
   Definitions
      At line 470 in file startup_stm32h723xx.s
   Uses
      At line 100 in file startup_stm32h723xx.s
      At line 332 in file startup_stm32h723xx.s

FDCAN3_IT0_IRQHandler 0000001E

Symbol: FDCAN3_IT0_IRQHandler
   Definitions
      At line 581 in file startup_stm32h723xx.s
   Uses
      At line 237 in file startup_stm32h723xx.s
      At line 443 in file startup_stm32h723xx.s

FDCAN3_IT1_IRQHandler 0000001E

Symbol: FDCAN3_IT1_IRQHandler
   Definitions
      At line 582 in file startup_stm32h723xx.s
   Uses
      At line 238 in file startup_stm32h723xx.s
      At line 444 in file startup_stm32h723xx.s

FDCAN_CAL_IRQHandler 0000001E



ARM Macro Assembler    Page 10 Alphabetic symbol ordering
Relocatable symbols


Symbol: FDCAN_CAL_IRQHandler
   Definitions
      At line 510 in file startup_stm32h723xx.s
   Uses
      At line 141 in file startup_stm32h723xx.s
      At line 372 in file startup_stm32h723xx.s

FLASH_IRQHandler 0000001E

Symbol: FLASH_IRQHandler
   Definitions
      At line 452 in file startup_stm32h723xx.s
   Uses
      At line 82 in file startup_stm32h723xx.s
      At line 313 in file startup_stm32h723xx.s

FMAC_IRQHandler 0000001E

Symbol: FMAC_IRQHandler
   Definitions
      At line 575 in file startup_stm32h723xx.s
   Uses
      At line 231 in file startup_stm32h723xx.s
      At line 437 in file startup_stm32h723xx.s

FMC_IRQHandler 0000001E

Symbol: FMC_IRQHandler
   Definitions
      At line 495 in file startup_stm32h723xx.s
   Uses
      At line 126 in file startup_stm32h723xx.s
      At line 357 in file startup_stm32h723xx.s

FPU_IRQHandler 0000001E

Symbol: FPU_IRQHandler
   Definitions
      At line 523 in file startup_stm32h723xx.s
   Uses
      At line 159 in file startup_stm32h723xx.s
      At line 385 in file startup_stm32h723xx.s

HSEM1_IRQHandler 0000001E

Symbol: HSEM1_IRQHandler
   Definitions
      At line 552 in file startup_stm32h723xx.s
   Uses
      At line 203 in file startup_stm32h723xx.s
      At line 414 in file startup_stm32h723xx.s

HardFault_Handler 0000000E

Symbol: HardFault_Handler
   Definitions
      At line 270 in file startup_stm32h723xx.s
   Uses



ARM Macro Assembler    Page 11 Alphabetic symbol ordering
Relocatable symbols

      At line 63 in file startup_stm32h723xx.s
      At line 271 in file startup_stm32h723xx.s

I2C1_ER_IRQHandler 0000001E

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 480 in file startup_stm32h723xx.s
   Uses
      At line 110 in file startup_stm32h723xx.s
      At line 342 in file startup_stm32h723xx.s

I2C1_EV_IRQHandler 0000001E

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 479 in file startup_stm32h723xx.s
   Uses
      At line 109 in file startup_stm32h723xx.s
      At line 341 in file startup_stm32h723xx.s

I2C2_ER_IRQHandler 0000001E

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 482 in file startup_stm32h723xx.s
   Uses
      At line 112 in file startup_stm32h723xx.s
      At line 344 in file startup_stm32h723xx.s

I2C2_EV_IRQHandler 0000001E

Symbol: I2C2_EV_IRQHandler
   Definitions
      At line 481 in file startup_stm32h723xx.s
   Uses
      At line 111 in file startup_stm32h723xx.s
      At line 343 in file startup_stm32h723xx.s

I2C3_ER_IRQHandler 0000001E

Symbol: I2C3_ER_IRQHandler
   Definitions
      At line 516 in file startup_stm32h723xx.s
   Uses
      At line 151 in file startup_stm32h723xx.s
      At line 378 in file startup_stm32h723xx.s

I2C3_EV_IRQHandler 0000001E

Symbol: I2C3_EV_IRQHandler
   Definitions
      At line 515 in file startup_stm32h723xx.s
   Uses
      At line 150 in file startup_stm32h723xx.s
      At line 377 in file startup_stm32h723xx.s

I2C4_ER_IRQHandler 0000001E




ARM Macro Assembler    Page 12 Alphabetic symbol ordering
Relocatable symbols

Symbol: I2C4_ER_IRQHandler
   Definitions
      At line 537 in file startup_stm32h723xx.s
   Uses
      At line 174 in file startup_stm32h723xx.s
      At line 399 in file startup_stm32h723xx.s

I2C4_EV_IRQHandler 0000001E

Symbol: I2C4_EV_IRQHandler
   Definitions
      At line 536 in file startup_stm32h723xx.s
   Uses
      At line 173 in file startup_stm32h723xx.s
      At line 398 in file startup_stm32h723xx.s

I2C5_ER_IRQHandler 0000001E

Symbol: I2C5_ER_IRQHandler
   Definitions
      At line 580 in file startup_stm32h723xx.s
   Uses
      At line 236 in file startup_stm32h723xx.s
      At line 442 in file startup_stm32h723xx.s

I2C5_EV_IRQHandler 0000001E

Symbol: I2C5_EV_IRQHandler
   Definitions
      At line 579 in file startup_stm32h723xx.s
   Uses
      At line 235 in file startup_stm32h723xx.s
      At line 441 in file startup_stm32h723xx.s

LPTIM1_IRQHandler 0000001E

Symbol: LPTIM1_IRQHandler
   Definitions
      At line 534 in file startup_stm32h723xx.s
   Uses
      At line 171 in file startup_stm32h723xx.s
      At line 396 in file startup_stm32h723xx.s

LPTIM2_IRQHandler 0000001E

Symbol: LPTIM2_IRQHandler
   Definitions
      At line 564 in file startup_stm32h723xx.s
   Uses
      At line 216 in file startup_stm32h723xx.s
      At line 426 in file startup_stm32h723xx.s

LPTIM3_IRQHandler 0000001E

Symbol: LPTIM3_IRQHandler
   Definitions
      At line 565 in file startup_stm32h723xx.s
   Uses
      At line 217 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 13 Alphabetic symbol ordering
Relocatable symbols

      At line 427 in file startup_stm32h723xx.s

LPTIM4_IRQHandler 0000001E

Symbol: LPTIM4_IRQHandler
   Definitions
      At line 566 in file startup_stm32h723xx.s
   Uses
      At line 218 in file startup_stm32h723xx.s
      At line 428 in file startup_stm32h723xx.s

LPTIM5_IRQHandler 0000001E

Symbol: LPTIM5_IRQHandler
   Definitions
      At line 567 in file startup_stm32h723xx.s
   Uses
      At line 219 in file startup_stm32h723xx.s
      At line 429 in file startup_stm32h723xx.s

LPUART1_IRQHandler 0000001E

Symbol: LPUART1_IRQHandler
   Definitions
      At line 568 in file startup_stm32h723xx.s
   Uses
      At line 220 in file startup_stm32h723xx.s
      At line 430 in file startup_stm32h723xx.s

LTDC_ER_IRQHandler 0000001E

Symbol: LTDC_ER_IRQHandler
   Definitions
      At line 531 in file startup_stm32h723xx.s
   Uses
      At line 167 in file startup_stm32h723xx.s
      At line 393 in file startup_stm32h723xx.s

LTDC_IRQHandler 0000001E

Symbol: LTDC_IRQHandler
   Definitions
      At line 530 in file startup_stm32h723xx.s
   Uses
      At line 166 in file startup_stm32h723xx.s
      At line 392 in file startup_stm32h723xx.s

MDIOS_IRQHandler 0000001E

Symbol: MDIOS_IRQHandler
   Definitions
      At line 549 in file startup_stm32h723xx.s
   Uses
      At line 198 in file startup_stm32h723xx.s
      At line 411 in file startup_stm32h723xx.s

MDIOS_WKUP_IRQHandler 0000001E

Symbol: MDIOS_WKUP_IRQHandler



ARM Macro Assembler    Page 14 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 548 in file startup_stm32h723xx.s
   Uses
      At line 197 in file startup_stm32h723xx.s
      At line 410 in file startup_stm32h723xx.s

MDMA_IRQHandler 0000001E

Symbol: MDMA_IRQHandler
   Definitions
      At line 550 in file startup_stm32h723xx.s
   Uses
      At line 200 in file startup_stm32h723xx.s
      At line 412 in file startup_stm32h723xx.s

MemManage_Handler 00000010

Symbol: MemManage_Handler
   Definitions
      At line 275 in file startup_stm32h723xx.s
   Uses
      At line 64 in file startup_stm32h723xx.s
      At line 276 in file startup_stm32h723xx.s

NMI_Handler 0000000C

Symbol: NMI_Handler
   Definitions
      At line 265 in file startup_stm32h723xx.s
   Uses
      At line 62 in file startup_stm32h723xx.s
      At line 266 in file startup_stm32h723xx.s

OCTOSPI1_IRQHandler 0000001E

Symbol: OCTOSPI1_IRQHandler
   Definitions
      At line 533 in file startup_stm32h723xx.s
   Uses
      At line 170 in file startup_stm32h723xx.s
      At line 395 in file startup_stm32h723xx.s

OCTOSPI2_IRQHandler 0000001E

Symbol: OCTOSPI2_IRQHandler
   Definitions
      At line 574 in file startup_stm32h723xx.s
   Uses
      At line 228 in file startup_stm32h723xx.s
      At line 436 in file startup_stm32h723xx.s

OTG_HS_EP1_IN_IRQHandler 0000001E

Symbol: OTG_HS_EP1_IN_IRQHandler
   Definitions
      At line 518 in file startup_stm32h723xx.s
   Uses
      At line 153 in file startup_stm32h723xx.s
      At line 380 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 15 Alphabetic symbol ordering
Relocatable symbols


OTG_HS_EP1_OUT_IRQHandler 0000001E

Symbol: OTG_HS_EP1_OUT_IRQHandler
   Definitions
      At line 517 in file startup_stm32h723xx.s
   Uses
      At line 152 in file startup_stm32h723xx.s
      At line 379 in file startup_stm32h723xx.s

OTG_HS_IRQHandler 0000001E

Symbol: OTG_HS_IRQHandler
   Definitions
      At line 520 in file startup_stm32h723xx.s
   Uses
      At line 155 in file startup_stm32h723xx.s
      At line 382 in file startup_stm32h723xx.s

OTG_HS_WKUP_IRQHandler 0000001E

Symbol: OTG_HS_WKUP_IRQHandler
   Definitions
      At line 519 in file startup_stm32h723xx.s
   Uses
      At line 154 in file startup_stm32h723xx.s
      At line 381 in file startup_stm32h723xx.s

PVD_AVD_IRQHandler 0000001E

Symbol: PVD_AVD_IRQHandler
   Definitions
      At line 449 in file startup_stm32h723xx.s
   Uses
      At line 79 in file startup_stm32h723xx.s
      At line 310 in file startup_stm32h723xx.s

PendSV_Handler 0000001A

Symbol: PendSV_Handler
   Definitions
      At line 298 in file startup_stm32h723xx.s
   Uses
      At line 74 in file startup_stm32h723xx.s
      At line 299 in file startup_stm32h723xx.s

RCC_IRQHandler 0000001E

Symbol: RCC_IRQHandler
   Definitions
      At line 453 in file startup_stm32h723xx.s
   Uses
      At line 83 in file startup_stm32h723xx.s
      At line 314 in file startup_stm32h723xx.s

RNG_IRQHandler 0000001E

Symbol: RNG_IRQHandler
   Definitions



ARM Macro Assembler    Page 16 Alphabetic symbol ordering
Relocatable symbols

      At line 522 in file startup_stm32h723xx.s
   Uses
      At line 158 in file startup_stm32h723xx.s
      At line 384 in file startup_stm32h723xx.s

RTC_Alarm_IRQHandler 0000001E

Symbol: RTC_Alarm_IRQHandler
   Definitions
      At line 489 in file startup_stm32h723xx.s
   Uses
      At line 119 in file startup_stm32h723xx.s
      At line 351 in file startup_stm32h723xx.s

RTC_WKUP_IRQHandler 0000001E

Symbol: RTC_WKUP_IRQHandler
   Definitions
      At line 451 in file startup_stm32h723xx.s
   Uses
      At line 81 in file startup_stm32h723xx.s
      At line 312 in file startup_stm32h723xx.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 249 in file startup_stm32h723xx.s
   Uses
      At line 61 in file startup_stm32h723xx.s
      At line 250 in file startup_stm32h723xx.s

SAI1_IRQHandler 0000001E

Symbol: SAI1_IRQHandler
   Definitions
      At line 529 in file startup_stm32h723xx.s
   Uses
      At line 165 in file startup_stm32h723xx.s
      At line 391 in file startup_stm32h723xx.s

SAI4_IRQHandler 0000001E

Symbol: SAI4_IRQHandler
   Definitions
      At line 571 in file startup_stm32h723xx.s
   Uses
      At line 224 in file startup_stm32h723xx.s
      At line 433 in file startup_stm32h723xx.s

SDMMC1_IRQHandler 0000001E

Symbol: SDMMC1_IRQHandler
   Definitions
      At line 496 in file startup_stm32h723xx.s
   Uses
      At line 127 in file startup_stm32h723xx.s
      At line 358 in file startup_stm32h723xx.s




ARM Macro Assembler    Page 17 Alphabetic symbol ordering
Relocatable symbols

SDMMC2_IRQHandler 0000001E

Symbol: SDMMC2_IRQHandler
   Definitions
      At line 551 in file startup_stm32h723xx.s
   Uses
      At line 202 in file startup_stm32h723xx.s
      At line 413 in file startup_stm32h723xx.s

SPDIF_RX_IRQHandler 0000001E

Symbol: SPDIF_RX_IRQHandler
   Definitions
      At line 538 in file startup_stm32h723xx.s
   Uses
      At line 175 in file startup_stm32h723xx.s
      At line 400 in file startup_stm32h723xx.s

SPI1_IRQHandler 0000001E

Symbol: SPI1_IRQHandler
   Definitions
      At line 483 in file startup_stm32h723xx.s
   Uses
      At line 113 in file startup_stm32h723xx.s
      At line 345 in file startup_stm32h723xx.s

SPI2_IRQHandler 0000001E

Symbol: SPI2_IRQHandler
   Definitions
      At line 484 in file startup_stm32h723xx.s
   Uses
      At line 114 in file startup_stm32h723xx.s
      At line 346 in file startup_stm32h723xx.s

SPI3_IRQHandler 0000001E

Symbol: SPI3_IRQHandler
   Definitions
      At line 498 in file startup_stm32h723xx.s
   Uses
      At line 129 in file startup_stm32h723xx.s
      At line 360 in file startup_stm32h723xx.s

SPI4_IRQHandler 0000001E

Symbol: SPI4_IRQHandler
   Definitions
      At line 526 in file startup_stm32h723xx.s
   Uses
      At line 162 in file startup_stm32h723xx.s
      At line 388 in file startup_stm32h723xx.s

SPI5_IRQHandler 0000001E

Symbol: SPI5_IRQHandler
   Definitions
      At line 527 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 18 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 163 in file startup_stm32h723xx.s
      At line 389 in file startup_stm32h723xx.s

SPI6_IRQHandler 0000001E

Symbol: SPI6_IRQHandler
   Definitions
      At line 528 in file startup_stm32h723xx.s
   Uses
      At line 164 in file startup_stm32h723xx.s
      At line 390 in file startup_stm32h723xx.s

SVC_Handler 00000016

Symbol: SVC_Handler
   Definitions
      At line 289 in file startup_stm32h723xx.s
   Uses
      At line 71 in file startup_stm32h723xx.s
      At line 290 in file startup_stm32h723xx.s

SWPMI1_IRQHandler 0000001E

Symbol: SWPMI1_IRQHandler
   Definitions
      At line 544 in file startup_stm32h723xx.s
   Uses
      At line 193 in file startup_stm32h723xx.s
      At line 406 in file startup_stm32h723xx.s

SysTick_Handler 0000001C

Symbol: SysTick_Handler
   Definitions
      At line 302 in file startup_stm32h723xx.s
   Uses
      At line 75 in file startup_stm32h723xx.s
      At line 303 in file startup_stm32h723xx.s

TAMP_STAMP_IRQHandler 0000001E

Symbol: TAMP_STAMP_IRQHandler
   Definitions
      At line 450 in file startup_stm32h723xx.s
   Uses
      At line 80 in file startup_stm32h723xx.s
      At line 311 in file startup_stm32h723xx.s

TIM15_IRQHandler 0000001E

Symbol: TIM15_IRQHandler
   Definitions
      At line 545 in file startup_stm32h723xx.s
   Uses
      At line 194 in file startup_stm32h723xx.s
      At line 407 in file startup_stm32h723xx.s

TIM16_IRQHandler 0000001E



ARM Macro Assembler    Page 19 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM16_IRQHandler
   Definitions
      At line 546 in file startup_stm32h723xx.s
   Uses
      At line 195 in file startup_stm32h723xx.s
      At line 408 in file startup_stm32h723xx.s

TIM17_IRQHandler 0000001E

Symbol: TIM17_IRQHandler
   Definitions
      At line 547 in file startup_stm32h723xx.s
   Uses
      At line 196 in file startup_stm32h723xx.s
      At line 409 in file startup_stm32h723xx.s

TIM1_BRK_IRQHandler 0000001E

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 472 in file startup_stm32h723xx.s
   Uses
      At line 102 in file startup_stm32h723xx.s
      At line 334 in file startup_stm32h723xx.s

TIM1_CC_IRQHandler 0000001E

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 475 in file startup_stm32h723xx.s
   Uses
      At line 105 in file startup_stm32h723xx.s
      At line 337 in file startup_stm32h723xx.s

TIM1_TRG_COM_IRQHandler 0000001E

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 474 in file startup_stm32h723xx.s
   Uses
      At line 104 in file startup_stm32h723xx.s
      At line 336 in file startup_stm32h723xx.s

TIM1_UP_IRQHandler 0000001E

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 473 in file startup_stm32h723xx.s
   Uses
      At line 103 in file startup_stm32h723xx.s
      At line 335 in file startup_stm32h723xx.s

TIM23_IRQHandler 0000001E

Symbol: TIM23_IRQHandler
   Definitions
      At line 583 in file startup_stm32h723xx.s
   Uses



ARM Macro Assembler    Page 20 Alphabetic symbol ordering
Relocatable symbols

      At line 239 in file startup_stm32h723xx.s
      At line 445 in file startup_stm32h723xx.s

TIM24_IRQHandler 0000001E

Symbol: TIM24_IRQHandler
   Definitions
      At line 584 in file startup_stm32h723xx.s
   Uses
      At line 240 in file startup_stm32h723xx.s
      At line 446 in file startup_stm32h723xx.s

TIM2_IRQHandler 0000001E

Symbol: TIM2_IRQHandler
   Definitions
      At line 476 in file startup_stm32h723xx.s
   Uses
      At line 106 in file startup_stm32h723xx.s
      At line 338 in file startup_stm32h723xx.s

TIM3_IRQHandler 0000001E

Symbol: TIM3_IRQHandler
   Definitions
      At line 477 in file startup_stm32h723xx.s
   Uses
      At line 107 in file startup_stm32h723xx.s
      At line 339 in file startup_stm32h723xx.s

TIM4_IRQHandler 0000001E

Symbol: TIM4_IRQHandler
   Definitions
      At line 478 in file startup_stm32h723xx.s
   Uses
      At line 108 in file startup_stm32h723xx.s
      At line 340 in file startup_stm32h723xx.s

TIM5_IRQHandler 0000001E

Symbol: TIM5_IRQHandler
   Definitions
      At line 497 in file startup_stm32h723xx.s
   Uses
      At line 128 in file startup_stm32h723xx.s
      At line 359 in file startup_stm32h723xx.s

TIM6_DAC_IRQHandler 0000001E

Symbol: TIM6_DAC_IRQHandler
   Definitions
      At line 501 in file startup_stm32h723xx.s
   Uses
      At line 132 in file startup_stm32h723xx.s
      At line 363 in file startup_stm32h723xx.s

TIM7_IRQHandler 0000001E




ARM Macro Assembler    Page 21 Alphabetic symbol ordering
Relocatable symbols

Symbol: TIM7_IRQHandler
   Definitions
      At line 502 in file startup_stm32h723xx.s
   Uses
      At line 133 in file startup_stm32h723xx.s
      At line 364 in file startup_stm32h723xx.s

TIM8_BRK_TIM12_IRQHandler 0000001E

Symbol: TIM8_BRK_TIM12_IRQHandler
   Definitions
      At line 490 in file startup_stm32h723xx.s
   Uses
      At line 121 in file startup_stm32h723xx.s
      At line 352 in file startup_stm32h723xx.s

TIM8_CC_IRQHandler 0000001E

Symbol: TIM8_CC_IRQHandler
   Definitions
      At line 493 in file startup_stm32h723xx.s
   Uses
      At line 124 in file startup_stm32h723xx.s
      At line 355 in file startup_stm32h723xx.s

TIM8_TRG_COM_TIM14_IRQHandler 0000001E

Symbol: TIM8_TRG_COM_TIM14_IRQHandler
   Definitions
      At line 492 in file startup_stm32h723xx.s
   Uses
      At line 123 in file startup_stm32h723xx.s
      At line 354 in file startup_stm32h723xx.s

TIM8_UP_TIM13_IRQHandler 0000001E

Symbol: TIM8_UP_TIM13_IRQHandler
   Definitions
      At line 491 in file startup_stm32h723xx.s
   Uses
      At line 122 in file startup_stm32h723xx.s
      At line 353 in file startup_stm32h723xx.s

UART4_IRQHandler 0000001E

Symbol: UART4_IRQHandler
   Definitions
      At line 499 in file startup_stm32h723xx.s
   Uses
      At line 130 in file startup_stm32h723xx.s
      At line 361 in file startup_stm32h723xx.s

UART5_IRQHandler 0000001E

Symbol: UART5_IRQHandler
   Definitions
      At line 500 in file startup_stm32h723xx.s
   Uses
      At line 131 in file startup_stm32h723xx.s



ARM Macro Assembler    Page 22 Alphabetic symbol ordering
Relocatable symbols

      At line 362 in file startup_stm32h723xx.s

UART7_IRQHandler 0000001E

Symbol: UART7_IRQHandler
   Definitions
      At line 524 in file startup_stm32h723xx.s
   Uses
      At line 160 in file startup_stm32h723xx.s
      At line 386 in file startup_stm32h723xx.s

UART8_IRQHandler 0000001E

Symbol: UART8_IRQHandler
   Definitions
      At line 525 in file startup_stm32h723xx.s
   Uses
      At line 161 in file startup_stm32h723xx.s
      At line 387 in file startup_stm32h723xx.s

UART9_IRQHandler 0000001E

Symbol: UART9_IRQHandler
   Definitions
      At line 577 in file startup_stm32h723xx.s
   Uses
      At line 233 in file startup_stm32h723xx.s
      At line 439 in file startup_stm32h723xx.s

USART10_IRQHandler 0000001E

Symbol: USART10_IRQHandler
   Definitions
      At line 578 in file startup_stm32h723xx.s
   Uses
      At line 234 in file startup_stm32h723xx.s
      At line 440 in file startup_stm32h723xx.s

USART1_IRQHandler 0000001E

Symbol: USART1_IRQHandler
   Definitions
      At line 485 in file startup_stm32h723xx.s
   Uses
      At line 115 in file startup_stm32h723xx.s
      At line 347 in file startup_stm32h723xx.s

USART2_IRQHandler 0000001E

Symbol: USART2_IRQHandler
   Definitions
      At line 486 in file startup_stm32h723xx.s
   Uses
      At line 116 in file startup_stm32h723xx.s
      At line 348 in file startup_stm32h723xx.s

USART3_IRQHandler 0000001E

Symbol: USART3_IRQHandler



ARM Macro Assembler    Page 23 Alphabetic symbol ordering
Relocatable symbols

   Definitions
      At line 487 in file startup_stm32h723xx.s
   Uses
      At line 117 in file startup_stm32h723xx.s
      At line 349 in file startup_stm32h723xx.s

USART6_IRQHandler 0000001E

Symbol: USART6_IRQHandler
   Definitions
      At line 514 in file startup_stm32h723xx.s
   Uses
      At line 149 in file startup_stm32h723xx.s
      At line 376 in file startup_stm32h723xx.s

UsageFault_Handler 00000014

Symbol: UsageFault_Handler
   Definitions
      At line 285 in file startup_stm32h723xx.s
   Uses
      At line 66 in file startup_stm32h723xx.s
      At line 286 in file startup_stm32h723xx.s

WAKEUP_PIN_IRQHandler 0000001E

Symbol: WAKEUP_PIN_IRQHandler
   Definitions
      At line 573 in file startup_stm32h723xx.s
   Uses
      At line 227 in file startup_stm32h723xx.s
      At line 435 in file startup_stm32h723xx.s

WWDG_IRQHandler 0000001E

Symbol: WWDG_IRQHandler
   Definitions
      At line 448 in file startup_stm32h723xx.s
   Uses
      At line 78 in file startup_stm32h723xx.s
      At line 309 in file startup_stm32h723xx.s

__user_initial_stackheap 00000020

Symbol: __user_initial_stackheap
   Definitions
      At line 606 in file startup_stm32h723xx.s
   Uses
      At line 604 in file startup_stm32h723xx.s
Comment: __user_initial_stackheap used once
150 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 43 in file startup_stm32h723xx.s
   Uses
      At line 47 in file startup_stm32h723xx.s
      At line 610 in file startup_stm32h723xx.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 32 in file startup_stm32h723xx.s
   Uses
      At line 35 in file startup_stm32h723xx.s
      At line 609 in file startup_stm32h723xx.s

__Vectors_Size 000002CC

Symbol: __Vectors_Size
   Definitions
      At line 244 in file startup_stm32h723xx.s
   Uses
      At line 58 in file startup_stm32h723xx.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

ExitRun0Mode 00000000

Symbol: ExitRun0Mode
   Definitions
      At line 251 in file startup_stm32h723xx.s
   Uses
      At line 255 in file startup_stm32h723xx.s
Comment: ExitRun0Mode used once
SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 252 in file startup_stm32h723xx.s
   Uses
      At line 257 in file startup_stm32h723xx.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 253 in file startup_stm32h723xx.s
   Uses
      At line 259 in file startup_stm32h723xx.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 603 in file startup_stm32h723xx.s
   Uses
      None
Comment: __use_two_region_memory unused
4 symbols
505 symbols in table
